﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_group_quest_contribution {

		[JsonProperty]
		public int? id { get; set; }

		[JsonProperty]
		public int? questid { get; set; }

		[JsonProperty]
		public int? progressid { get; set; }

		[JsonProperty]
		public int? playerid { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string playername { get; set; }

		[JsonProperty]
		public int? guildid { get; set; }

		[JsonProperty]
		public int? factionid { get; set; }

		[JsonProperty]
		public int? actiontype { get; set; }

		[JsonProperty]
		public int? targetid { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string targetname { get; set; }

		[JsonProperty]
		public int? contributioncount { get; set; }

		[JsonProperty]
		public DateTime? contributiontime { get; set; }

		[JsonProperty]
		public bool? hasreceivedreward { get; set; }

	}

}
