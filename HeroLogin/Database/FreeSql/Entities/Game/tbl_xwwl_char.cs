﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_xwwl_char {

		[JsonProperty]
		public int? id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_name { get; set; }

		[JsonProperty]
		public int? fld_index { get; set; }

		[JsonProperty]
		public int? fld_level { get; set; }

		[JsonProperty]
		public byte[] fld_face { get; set; }

		[JsonProperty]
		public int? fld_job { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_exp { get; set; }

		[JsonProperty]
		public int? fld_zx { get; set; }

		[JsonProperty]
		public int? fld_job_level { get; set; }

		[JsonProperty]
		public double? fld_x { get; set; }

		[JsonProperty]
		public double? fld_y { get; set; }

		[JsonProperty]
		public double? fld_z { get; set; }

		[JsonProperty]
		public int? fld_menow { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_money { get; set; }

		[JsonProperty]
		public int? fld_hp { get; set; }

		[JsonProperty]
		public int? fld_mp { get; set; }

		[JsonProperty]
		public int? fld_sp { get; set; }

		[JsonProperty]
		public int? fld_wx { get; set; }

		[JsonProperty]
		public int? fld_se { get; set; }

		[JsonProperty]
		public int? fld_point { get; set; }

		[JsonProperty]
		public byte[] fld_skills { get; set; }

		[JsonProperty]
		public byte[] fld_wearitem { get; set; }

		[JsonProperty]
		public byte[] fld_item { get; set; }

		[JsonProperty]
		public byte[] fld_qitem { get; set; }

		[JsonProperty]
		public byte[] fld_ntcitem { get; set; }

		[JsonProperty]
		public byte[] fld_coatitem { get; set; }

		[JsonProperty]
		public byte[] fld_kongfu { get; set; }

		[JsonProperty]
		public byte[] fld_hits { get; set; }

		[JsonProperty]
		public byte[] fld_doors { get; set; }

		[JsonProperty]
		public byte[] fld_quest { get; set; }

		[JsonProperty]
		public int? fld_lumpid { get; set; }

		[JsonProperty]
		public int? fld_fight_exp { get; set; }

		[JsonProperty]
		public int? fld_j9 { get; set; }

		[JsonProperty]
		public int? fld_jq { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_jl { get; set; }

		[JsonProperty]
		public byte[] fld_nametype { get; set; }

		[JsonProperty]
		public int? fld_zbver { get; set; }

		[JsonProperty]
		public int? fld_zztype { get; set; }

		[JsonProperty]
		public int? fld_zzsl { get; set; }

		[JsonProperty]
		public byte[] fld_ctime { get; set; }

		[JsonProperty]
		public byte[] fld_ctimenew { get; set; }

		[JsonProperty]
		public byte[] fld_stime { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_qlname { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_qljzname { get; set; }

		[JsonProperty]
		public int? fld_qldu { get; set; }

		[JsonProperty]
		public int? fld_qldumax { get; set; }

		[JsonProperty]
		public int? fld_qlrank { get; set; }

		[JsonProperty]
		public byte[] fld_thangthienkhicong { get; set; }

		[JsonProperty]
		public byte[] fld_thangthienvocong { get; set; }

		[JsonProperty]
		public int? fld_thangthienlichluyen { get; set; }

		[JsonProperty]
		public int? fld_thangthienvocongdiemso { get; set; }

		[JsonProperty]
		public int? fld_add_hp { get; set; }

		[JsonProperty]
		public int? fld_add_at { get; set; }

		[JsonProperty]
		public int? fld_add_df { get; set; }

		[JsonProperty]
		public int? fld_add_hb { get; set; }

		[JsonProperty]
		public int? fld_add_mp { get; set; }

		[JsonProperty]
		public int? fld_add_mz { get; set; }

		[JsonProperty]
		public int? fld_zs { get; set; }

		[JsonProperty]
		public int? fld_online { get; set; }

		[JsonProperty]
		public int? fld_get_wx { get; set; }

		[JsonProperty]
		public int? fld_tongkim { get; set; }

		[JsonProperty]
		public int? fld_taisinh { get; set; }

		[JsonProperty]
		public int? fld_vipdj { get; set; }

		[JsonProperty]
		public byte[] 在线时间 { get; set; }

		[JsonProperty]
		public int? fld_七彩 { get; set; }

		[JsonProperty]
		public int? fld_vip_at { get; set; }

		[JsonProperty]
		public int? fld_vip_df { get; set; }

		[JsonProperty]
		public int? fld_vip_hp { get; set; }

		[JsonProperty]
		public int? fld_vip_level { get; set; }

		[JsonProperty]
		public int? fld_zscs { get; set; }

		[JsonProperty]
		public int? fld_sjjl { get; set; }

		[JsonProperty]
		public double? fld_在线时间 { get; set; }

		[JsonProperty]
		public int? fld_在线等级 { get; set; }

		[JsonProperty]
		public int? fld_领奖标志 { get; set; }

		[JsonProperty, Column(Name = " ")]
		public int? __ { get; set; }

		[JsonProperty]
		public int? fld_签名类型 { get; set; }

		[JsonProperty]
		public int? fld_任务等级4 { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_师傅 { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_徒弟1 { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_徒弟2 { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_徒弟3 { get; set; }

		[JsonProperty]
		public int? fld_师徒武功1_1 { get; set; }

		[JsonProperty]
		public int? fld_师徒武功1_2 { get; set; }

		[JsonProperty]
		public int? fld_师徒武功1_3 { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_dayquest { get; set; }

		[JsonProperty]
		public int? fld_tlc { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_fqid { get; set; }

		[JsonProperty]
		public long? solangietnguoi { get; set; }

		[JsonProperty]
		public long? bigietsolan { get; set; }

		[JsonProperty]
		public byte[] fld_suphu { get; set; }

		[JsonProperty]
		public byte[] fld_detu { get; set; }

		[JsonProperty]
		public byte[] fld_sudovocong { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_giaitruthoigian { get; set; }

		[JsonProperty]
		public int? fld_titlepoints { get; set; }

		[JsonProperty]
		public DateTime? congthanhchienthoigian { get; set; }

		[JsonProperty]
		public int? fld_xb { get; set; }

		[JsonProperty]
		public int? fld_rosetitlepoints { get; set; }

		[JsonProperty]
		public int? fld_speakingtype { get; set; }

		[JsonProperty]
		public byte[] fld_nszitem { get; set; }

		[JsonProperty]
		public byte[] fld_ljkongfu { get; set; }

		[JsonProperty]
		public int? bangphai_doconghien { get; set; }

		[JsonProperty]
		public int? fld_mlz { get; set; }

		[JsonProperty]
		public int? fld_pvp_piont { get; set; }

		[JsonProperty]
		public int? fld_thannuvocongdiemso { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_love_word { get; set; }

		[JsonProperty]
		public int? fld_marital_status { get; set; }

		[JsonProperty]
		public int? fld_married { get; set; }

		[JsonProperty]
		public DateTime? fld_jh_date { get; set; }

		[JsonProperty]
		public int? fld_fb_time { get; set; }

		[JsonProperty]
		public int? fld_lost_wx { get; set; }

		[JsonProperty]
		public int? fld_hd_time { get; set; }

		[JsonProperty]
		public byte[] fld_kieutoc { get; set; }

		[JsonProperty]
		public byte[] fld_khuonmat { get; set; }

		[JsonProperty]
		public int? fld_whtb { get; set; }

		[JsonProperty]
		public byte[] fld_chtime { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_config { get; set; }

		[JsonProperty]
		public byte[] fld_fashion_item { get; set; }

		[JsonProperty]
		public byte[] fld_quest_finish { get; set; }

		[JsonProperty]
		public int? fld_add_clvc { get; set; }

		[JsonProperty]
		public int? fld_add_ptvc { get; set; }

		[JsonProperty]
		public int? fld_add_kc { get; set; }

		[JsonProperty]
		public int? version { get; set; }

		[JsonProperty]
		public bool? nhanqualandau { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string tlc_random_phe { get; set; }

		[JsonProperty]
		public int? vohuan_gioihan_theongay { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string vohuan_time { get; set; }

		[JsonProperty]
		public int? fld_moneyextralevel { get; set; }

		[JsonProperty]
		public byte[] fld_pinkbag_item { get; set; }

		[JsonProperty]
		public byte[] fld_asc7_anti_qigong { get; set; }

	}

}
