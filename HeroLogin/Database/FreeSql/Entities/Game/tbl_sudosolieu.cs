﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_sudosolieu {

		[JsonProperty]
		public int? id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_tname { get; set; }

		[JsonProperty]
		public int? fld_index { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_sname { get; set; }

		[JsonProperty]
		public int? fld_tlevel { get; set; }

		[JsonProperty]
		public int? fld_stlevel { get; set; }

		[JsonProperty]
		public int? fld_styhd { get; set; }

		[JsonProperty]
		public int? fld_stwg1 { get; set; }

		[JsonProperty]
		public int? fld_stwg2 { get; set; }

		[JsonProperty]
		public int? fld_stwg3 { get; set; }

	}

}
