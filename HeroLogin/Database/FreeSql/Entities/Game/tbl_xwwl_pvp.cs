﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_xwwl_pvp {

		[JsonProperty, Column(StringLength = -2)]
		public string santapten { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string a_nguoichoi { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string b_nguoichoi { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string agietnguoisoluong { get; set; }

		[Json<PERSON>roperty, Column(StringLength = -2)]
		public string bgietnguoisoluong { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string a_chaytronsolan { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string b_chaytronsolan { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string athuduocnguyenbao { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string bthuduocnguyenbao { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string tranhtaiketqua { get; set; }

	}

}
