﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	/// <summary>
	/// Records of item synthesis/crafting activities - <PERSON><PERSON> lại hoạt động tổng hợp/chế tạo vật phẩm
	/// </summary>
	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class syntheticrecord {

		/// <summary>
		/// Primary key - Khóa chính
		/// </summary>
		[JsonProperty, Column(IsPrimary = true, IsIdentity = true, InsertValueSql = "nextval('syntheticrecord_id_seq'::regclass)")]
		public int id { get; set; }

		/// <summary>
		/// User/Account ID - ID tài khoản
		/// </summary>
		[JsonProperty, Column(StringLength = 30, IsNullable = false)]
		public string fld_id { get; set; }

		/// <summary>
		/// Character name - Tên nhân vật
		/// </summary>
		[JsonProperty, Column(StringLength = 30, IsNullable = false)]
		public string fld_name { get; set; }

		/// <summary>
		/// Item global ID (unique instance) - ID toàn cục của vật phẩm
		/// </summary>
		[JsonProperty]
		public long? fld_qjid { get; set; }

		/// <summary>
		/// Item template ID - ID template vật phẩm
		/// </summary>
		[JsonProperty]
		public int? fld_pid { get; set; }

		/// <summary>
		/// Item name - Tên vật phẩm
		/// </summary>
		[JsonProperty, Column(StringLength = 50)]
		public string fld_iname { get; set; }

		/// <summary>
		/// Item magic property 0 - Thuộc tính phép thuật 0
		/// </summary>
		[JsonProperty]
		public int? fld_magic0 { get; set; } = 0;

		/// <summary>
		/// Item magic property 1 - Thuộc tính phép thuật 1
		/// </summary>
		[JsonProperty]
		public int? fld_magic1 { get; set; } = 0;

		/// <summary>
		/// Item magic property 2 - Thuộc tính phép thuật 2
		/// </summary>
		[JsonProperty]
		public int? fld_magic2 { get; set; } = 0;

		/// <summary>
		/// Item magic property 3 - Thuộc tính phép thuật 3
		/// </summary>
		[JsonProperty]
		public int? fld_magic3 { get; set; } = 0;

		/// <summary>
		/// Item magic property 4 - Thuộc tính phép thuật 4
		/// </summary>
		[JsonProperty]
		public int? fld_magic4 { get; set; } = 0;

		/// <summary>
		/// Type of synthesis (Upgrade, Craft, Enhance) - Loại tổng hợp
		/// </summary>
		[JsonProperty, Column(StringLength = 30)]
		public string fld_type { get; set; }

		/// <summary>
		/// Operation/Recipe ID - ID công thức/thao tác
		/// </summary>
		[JsonProperty]
		public int? fld_czid { get; set; }

		/// <summary>
		/// Result status (Success, Failed) - Trạng thái kết quả
		/// </summary>
		[JsonProperty, Column(StringLength = 30)]
		public string fld_success { get; set; }

		/// <summary>
		/// Enhancement level - Cấp độ cường hóa
		/// </summary>
		[JsonProperty]
		public int? fld_qhjd { get; set; } = 0;

		/// <summary>
		/// Record creation timestamp - Thời gian tạo bản ghi
		/// </summary>
		[JsonProperty, Column(InsertValueSql = "CURRENT_TIMESTAMP")]
		public DateTime? created_at { get; set; }

	}

}
