﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Public {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class giftcode_rewards {

		[JsonProperty]
		public int? id { get; set; }

		[JsonProperty]
		public int? type { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string rewards { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string note { get; set; }

	}

}
