using System;
using System.Linq;
using System.Threading.Tasks;

namespace HeroYulgang.Database.FreeSql
{
    /// <summary>
    /// Simple Services static class - like Node.js modules
    /// Easy access to all database services
    /// </summary>
    public static class Services
    {
        /// <summary>
        /// Public database service for template data
        /// </summary>
        public static class Public
        {
            /// <summary>
            /// Initialize PublicDb and load all templates
            /// Call this from World.SetItme()
            /// </summary>
            public static async Task<bool> InitializeAsync()
            {
                try
                {
                    Console.WriteLine("Initializing Public database services...");
                    
                    // Initialize PublicDb
                    var success = await PublicDb.InitializeAsync();
                    if (!success)
                    {
                        Console.WriteLine("✗ Failed to initialize PublicDb");
                        return false;
                    }

                    // Load templates into World collections for backward compatibility

                    Console.WriteLine("✓ Public database services initialized successfully");
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"✗ Failed to initialize Public services: {ex.Message}");
                    return false;
                }
            }
            public static bool IsReady => PublicDb.IsReady;
        }

        /// <summary>
        /// Account database service (placeholder for future)
        /// </summary>
        public static class Account
        {
            public static async Task<bool> InitializeAsync()
            {
                // TODO: Implement AccountDb
                await AccountDb.InitializeAsync();
                return true;
            }
        }

        /// <summary>
        /// Game database service
        /// </summary>
        public static class Game
        {
            public static async Task<bool> InitializeAsync()
            {
                // Initialize GameDb and load guild members
                await GameDb.InitializeAsync();
                return true;
            }
        }

        /// <summary>
        /// BBG/Marketplace database service
        /// </summary>
        public static class BBG
        {
            public static async Task<bool> InitializeAsync()
            {
                BBGDb.Initialize();
                return true;
            }
        }
    }
}
