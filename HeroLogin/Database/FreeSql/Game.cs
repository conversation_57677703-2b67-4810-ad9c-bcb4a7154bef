

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FreeSql;
using HeroYulgang.Database.FreeSql.Entities.Game;
using RxjhServer;
using HeroLogin.Core;
using HeroLogin.Services;

namespace HeroYulgang.Database.FreeSql;

public static class GameDb
{
    private static IFreeSql? _freeSql;
    private static bool _isInitialized = false;
    private static readonly object _lock = new object();

    public static async Task<bool> InitializeAsync()
    {
        try
        {
            if (_isInitialized) return true;

            lock (_lock)
            {
                if (_isInitialized) return true;

                var connectionString = ConfigManager.Instance.PogresSettings.GameDb;
                Console.WriteLine("GameDb connection string: " + connectionString);
                // Create FreeSql instance
                _freeSql = new FreeSqlBuilder()
                    .UseConnectionString(DataType.PostgreSQL, connectionString)
                    .UseAutoSyncStructure(false)
                    .UseNoneCommandParameter(true)
                    .Build();
                Logger.Instance.Info("✓ GameDb initialized successfully");
                _isInitialized = true;
            }
            return true;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to initialize GameDb: {ex.Message}");
            return false;
        }
    }

    public static List<tbl_group_quest> LoadQuestDefinitions()
    {
        try
        {
            if (_freeSql == null) return null;

            var quests = _freeSql.Select<tbl_group_quest>().Where(q => q.isactive ?? false).ToList();
            return quests;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to load quest definitions: {ex.Message}");
            return null;
        }
    }

    public static bool CheckQuestActive(int questId)
    {
        try
        {
            if (_freeSql == null) return false;

            var quest = _freeSql.Select<tbl_group_quest>().Where(q => q.id == questId && q.isactive == true).Count();
            return quest > 0;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to check quest active: {ex.Message}");
            return false;
        }
    }

    public static async Task<int> FindGroupQuestId(int questId)
    {
        try
        {
            if (_freeSql == null) return 0;

            var quest = await _freeSql.Select<tbl_group_quest>().Where(q => q.id == questId).CountAsync();
            return (int)quest;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to find quest ID: {ex.Message}");
            return 0;
        }
    }

    public static async Task<int> AddGroupQuest(tbl_group_quest quest)
    {
        try
        {
            if (_freeSql == null) return 0;

            var result = await _freeSql.Insert<tbl_group_quest>().AppendData(quest).ExecuteAffrowsAsync();
            return (int)result;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to add quest: {ex.Message}");
            return 0;
        }
    }

    public static async Task<int> UpdateGroupQuest(tbl_group_quest quest)
    {
        try
        {
            if (_freeSql == null) return 0;

            var result = await _freeSql.Update<tbl_group_quest>().SetSource(quest).ExecuteAffrowsAsync();
            return (int)result;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to update quest: {ex.Message}");
            return 0;
        }
    }

    public static async Task<List<tbl_guild_quest_progress>> LoadGuildQuestProcessToday()
    {
        try
        {
            if (_freeSql == null) return null;

            var quests = await _freeSql.Select<tbl_guild_quest_progress>().Where(x => x.status != 4 && (x.status == 1 || x.status == 2 || (x.status == 3 && x.completedtime.Value.Date == DateTime.Now.Date))).ToListAsync();
            return quests;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to load guild quest progress: {ex.Message}");
            return null;
        }
    }

    public static async Task<List<tbl_group_quest_contribution>> LoadAllGroupQuestContributions()
    {
        try
        {
            if (_freeSql == null) return null;

            var quests = await _freeSql.Select<tbl_group_quest_contribution>().ToListAsync();
            return quests;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to load group quest contributions: {ex.Message}");
            return null;
        }
    }

    public static async Task<tbl_guild_quest_progress> GetGuildQuestProgressToday(int guildId, int questId)
    {
        try
        {
            if (_freeSql == null) return null;

            var quest = await _freeSql.Select<tbl_guild_quest_progress>()
            .Where(x => x.guildid == guildId && x.questid == questId)
            .Where(x => x.status == 1 || x.status == 2 || (x.status == 3 && x.completedtime.Value.Date == DateTime.Now.Date))
            .FirstAsync();
            return quest;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to get guild quest progress: {ex.Message}");
            return null;
        }
    }

    public static async Task<tbl_guild_quest_progress> CreateGuildQuestProgress(tbl_guild_quest_progress progress)
    {
        try
        {
            if (_freeSql == null) return null;

            var result = await _freeSql.Insert<tbl_guild_quest_progress>().AppendData(progress).ExecuteAffrowsAsync();
            return progress;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to create guild quest progress: {ex.Message}");
            return null;
        }
    }

    public static async Task<tbl_guild_quest_progress> UpdateGuildQuestProgress(tbl_guild_quest_progress progress)
    {
        try
        {
            if (_freeSql == null) return null;

            var result = await _freeSql.Update<tbl_guild_quest_progress>().SetSource(progress).ExecuteAffrowsAsync();
            return progress;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to update guild quest progress: {ex.Message}");
            return null;
        }
    }


    public static async Task<tbl_group_quest_contribution> FindGuildQuestContribution(int questId, int playerId, int guildId, int progressId)
    {
        try
        {
            if (_freeSql == null) return null;

            var quest = await _freeSql.Select<tbl_group_quest_contribution>()
            .Where(x => x.questid == questId && x.playerid == playerId && x.guildid == guildId && x.progressid == progressId)
            .FirstAsync();
            return quest;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to find guild quest contribution: {ex.Message}");
            return null;
        }
    }

    public static async Task<tbl_group_quest_contribution> CreateGuildQuestContribution(tbl_group_quest_contribution contribution)
    {
        try
        {
            if (_freeSql == null) return null;

            var result = await _freeSql.Insert<tbl_group_quest_contribution>().AppendData(contribution).ExecuteAffrowsAsync();
            return contribution;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to create guild quest contribution: {ex.Message}");
            return null;
        }
    }

    public static async Task<tbl_group_quest_contribution> UpdateGuildQuestContribution(tbl_group_quest_contribution contribution)
    {
        try
        {
            if (_freeSql == null) return null;

            var result = await _freeSql.Update<tbl_group_quest_contribution>().SetSource(contribution).ExecuteAffrowsAsync();
            return contribution;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to update guild quest contribution: {ex.Message}");
            return null;
        }
    }

    public static async Task<bool> UpdateGroupQuestContributionRewardReceived(int contributionId)
    {
        try
        {
            if (_freeSql == null) return false;

            var result = await _freeSql.Update<tbl_group_quest_contribution>()
            .Set(x => x.hasreceivedreward, true)
            .Where(x => x.id == contributionId)
            .ExecuteAffrowsAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to update guild quest contribution reward received: {ex.Message}");
            return false;
        }
    }

    public static async Task<tbl_xwwl_guild> FindGUildById(int guildId)
    {
        try
        {
            if (_freeSql == null) return null;

            var guild = await _freeSql.Select<tbl_xwwl_guild>()
            .Where(x => x.id == guildId)
            .FirstAsync();
            return guild;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to find guild: {ex.Message}");
            return null;
        }
    }

    public static async Task<tbl_group_quest_history> AddQuestHistory(tbl_group_quest_history history)
    {
        try
        {
            if (_freeSql == null) return null;

            var result = await _freeSql.Insert<tbl_group_quest_history>().AppendData(history).ExecuteAffrowsAsync();
            return history;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to add quest history: {ex.Message}");
            return null;
        }
    }

}
