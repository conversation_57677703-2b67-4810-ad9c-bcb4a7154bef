
using System.Threading.Tasks;
using Grpc.Core;
using HeroLogin.Core;
using HeroLogin.Protos;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Database.FreeSql.Entities.Account;
using RxjhServer.GroupQuest;
using Microsoft.AspNetCore.Connections;

namespace HeroLogin.Services
{
    public class LoginAuthService : LoginAuth.LoginAuthBase
    {
        private readonly ILogger<LoginAuthService> _logger;
        private readonly ConfigManager _configManager;

        // Static instance để có thể truy cập từ bên ngoài
        private static LoginAuthService? _instance;
        public static LoginAuthService? Instance => _instance;

        public LoginAuthService(ILogger<LoginAuthService> logger)
        {
            _logger = logger;
            _configManager = ConfigManager.Instance;

            // Lưu instance
            _instance = this;
        }

        public override async Task<VerifyTokenResponse> VerifyToken(VerifyTokenRequest request, ServerCallContext context)
        {
            _logger.LogInformation($"Yêu cầu xác thực token cho tài khoản {request.AccountId}");

            try
            {
                // Tìm tài khoản trong cơ sở dữ liệu sử dụng FreeSql
                var account = await AccountDb.FindAccount(request.AccountId);

                if (account == null)
                {
                    _logger.LogWarning($"Tài khoản {request.AccountId} không tồn tại");
                    return new VerifyTokenResponse
                    {
                        IsValid = false,
                        AccountId = request.AccountId,
                        ErrorMessage = "Tài khoản không tồn tại"
                    };
                }

                // Kiểm tra token
                if (account.fld_passkey != request.Token)
                {
                    _logger.LogWarning($"Token không hợp lệ cho tài khoản {request.AccountId}");
                    return new VerifyTokenResponse
                    {
                        IsValid = false,
                        AccountId = request.AccountId,
                        ErrorMessage = "Token không hợp lệ"
                    };
                }

                // Kiểm tra thời gian token
                if (!string.IsNullOrEmpty(account.fld_passkey_timestamp))
                {
                    if (DateTime.TryParse(account.fld_passkey_timestamp, out DateTime timestamp))
                    {
                        // Token hết hạn sau 5 phút
                        if ((DateTime.Now - timestamp).TotalMinutes > 5)
                        {
                            _logger.LogWarning($"Token đã hết hạn cho tài khoản {request.AccountId}");
                            return new VerifyTokenResponse
                            {
                                IsValid = false,
                                AccountId = request.AccountId,
                                ErrorMessage = "Token đã hết hạn"
                            };
                        }
                    }
                }

                // Lưu thông tin client đã đăng nhập vào GameServerManager
                // Lấy thông tin IP từ context
                string clientIP = context.Peer ?? "Unknown";

                _logger.LogInformation($"Xác thực token thành công cho tài khoản {request.AccountId}");
                return new VerifyTokenResponse
                {
                    IsValid = true,
                    AccountId = request.AccountId,
                    ErrorMessage = ""
                };
            }
            catch (Exception ex)
            {
                _logger.LogError($"Lỗi khi xác thực token: {ex.Message}");
                return new VerifyTokenResponse
                {
                    IsValid = false,
                    AccountId = request.AccountId,
                    ErrorMessage = "Lỗi hệ thống"
                };
            }
        }


        public override async Task<RegisterGameServerResponse> RegisterGameServer(RegisterGameServerRequest request, ServerCallContext context)
        {
            _logger.LogInformation($"Yêu cầu đăng ký GameServer: {request.ServerName} (Cluster: {request.ClusterId}, Server: {request.ServerId})");

            try
            {
                // Tìm server trong ClusterManager
                var server = ClusterManager.Instance.GetServerChannel(request.ClusterId, request.ServerId);
                if (server == null)
                {
                    _logger.LogWarning($"Server {request.ServerId} không tồn tại trong cluster {request.ClusterId}");
                    return new RegisterGameServerResponse
                    {
                        Success = false,
                        Message = $"Server {request.ServerId} không tồn tại trong cluster {request.ClusterId}"
                    };
                }

                // Log thông tin để so sánh tên server
                if (request.ServerName != server.ServerName)
                {
                    _logger.LogInformation($"GameServer gửi tên '{request.ServerName}' nhưng LoginServer config có tên '{server.ServerName}' - sẽ sử dụng tên từ LoginServer config");
                }

                // Cập nhật thông tin server trong ClusterManager (giữ nguyên ServerName từ config)
                // KHÔNG cập nhật server.ServerName - giữ nguyên tên từ LoginServer config
                server.ServerIP = request.ServerIp;
                server.GameServerPort = request.ServerPort;
                server.GameServerGrpcPort = request.GrpcPort;
                server.Status = true;

                // Cập nhật thông tin trong cơ sở dữ liệu (sử dụng ServerName từ LoginServer config)
                await AccountDb.UpdateServerStatus(server.ServerName, request.ServerId, 0, server.MaximumOnline);

                _logger.LogInformation($"Đăng ký GameServer thành công: {server.ServerName} (GameServer gửi tên: {request.ServerName})");
                return new RegisterGameServerResponse
                {
                    Success = true,
                    Message = "Đăng ký GameServer thành công"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError($"Lỗi khi đăng ký GameServer: {ex.Message}");
                return new RegisterGameServerResponse
                {
                    Success = false,
                    Message = $"Lỗi hệ thống: {ex.Message}"
                };
            }
        }
        /// <summary>
        /// Xác thực tài khoản đăng nhập from GameServer
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<ValidateAccountLoginResponse> ValidateAccountLogin(ValidateAccountLoginRequest request, ServerCallContext context)
        {
            _logger.LogInformation($"Yêu cầu xác thực tài khoản: {request.AccountId}");

            try
            {
                // Tìm tài khoản trong cơ sở dữ liệu sử dụng FreeSql
                var account = await AccountDb.FindAccount(request.AccountId);

                if (account == null)
                {
                    _logger.LogWarning($"Tài khoản {request.AccountId} không tồn tại");
                    return new ValidateAccountLoginResponse
                    {
                        IsValid = false,
                        ErrorMessage = "Tài khoản không tồn tại"
                    };
                }

                var passKey = account.fld_passkey;
                var tempKey = account.fld_temp_passkey;
                _logger.LogInformation($"Passkey: {passKey}, TempKey: {tempKey}, Request: {request.Password}");

                // Kiểm tra xem tài khoản có bị khóa không
                if (account.fld_lock == 1)
                {
                    _logger.LogWarning($"Tài khoản {request.AccountId} đã bị khóa");
                    return new ValidateAccountLoginResponse
                    {
                        IsValid = false,
                        ErrorMessage = "Tài khoản đã bị khóa"
                    };
                }

                if (passKey != request.Password)
                {
                    if (request.Password.Length == 32 && (tempKey != request.Password || tempKey.Length != 32))
                    {
                        _logger.LogWarning($"Mật khẩu không chính xác cho tài khoản {request.AccountId}");
                        return new ValidateAccountLoginResponse
                        {
                            IsValid = false,
                            ErrorMessage = "Mật khẩu không chính xác"
                        };
                    }
                }
                //if (account.FldZt > 0)
                //{
                //    _logger.LogWarning($"Tài khoản {request.AccountId} đã bị khóa");
                //    return new ValidateAccountLoginResponse
                //    {
                //        IsValid = false,
                //        ErrorMessage = $"Tài khoản đã bị khóa ZT={account.FldZt}"
                //    };
                //}

                _logger.LogInformation($"Xác thực tài khoản thành công: {request.AccountId}");
                return new ValidateAccountLoginResponse
                {
                    IsValid = true,
                    ErrorMessage = "",
                    AccountId = request.AccountId,
                    LoginIp = request.UserIp,
                    LoginPort = request.UserPort.ToString()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError($"Lỗi khi xác thực tài khoản: {ex.Message}");
                return new ValidateAccountLoginResponse
                {
                    IsValid = false,
                    ErrorMessage = "Lỗi hệ thống"
                };
            }
        }

        public override async Task<UpdateGameServerStatusResponse> UpdateGameServerStatus(UpdateGameServerStatusRequest request, ServerCallContext context)
        {
            _logger.LogInformation($"Yêu cầu cập nhật trạng thái GameServer: Cluster {request.ClusterId}, Server {request.ServerId}, Online: {request.IsOnline}, Count: {request.OnlineCount}");

            try
            {
                // Tìm server trong ClusterManager
                var server = ClusterManager.Instance.GetServerChannel(request.ClusterId, request.ServerId);
                if (server == null)
                {
                    _logger.LogWarning($"Server {request.ServerId} không tồn tại trong cluster {request.ClusterId}");
                    return new UpdateGameServerStatusResponse
                    {
                        Success = false,
                        Message = $"Server {request.ServerId} không tồn tại trong cluster {request.ClusterId}"
                    };
                }

                // Cập nhật trạng thái server trong ClusterManager
                server.Status = request.IsOnline;
                server.CurrentPlayers = request.OnlineCount;

                // Cập nhật thông tin trong cơ sở dữ liệu
                await AccountDb.UpdateServerStatus(server.ServerName, request.ServerId, request.OnlineCount, server.MaximumOnline);

                // _logger.LogInformation($"Cập nhật trạng thái GameServer thành công: {server.ServerName}");
                return new UpdateGameServerStatusResponse
                {
                    Success = true,
                    Message = "Cập nhật trạng thái GameServer thành công"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError($"Lỗi khi cập nhật trạng thái GameServer: {ex.Message}");
                return new UpdateGameServerStatusResponse
                {
                    Success = false,
                    Message = $"Lỗi hệ thống: {ex.Message}"
                };
            }
        }

        public override async Task TransmitMessage(IAsyncStreamReader<TransmitMessageRequest> requestStream, IServerStreamWriter<TransmitMessageResponse> responseStream, ServerCallContext context)
        {
            int? currentClusterId = null;
            int? currentServerId = null;

            try
            {
                while (await requestStream.MoveNext())
                {
                    var request = requestStream.Current;
                    try
                    {
                        // Lưu thông tin cluster và server ID để cleanup sau này
                        currentClusterId = request.ClusterId;
                        currentServerId = request.ServerId;

                        // Lưu stream writer vào ClusterManager để có thể gửi message ngược lại
                        ClusterManager.Instance.SetTransmitMessageWriter(request.ClusterId, request.ServerId, responseStream);

                        // Log tin nhắn nhận được từ GameServer
                        _logger.LogInformation($"[TransmitMessage] Nhận tin nhắn từ GameServer - Cluster: {request.ClusterId}, Server: {request.ServerId}");
                        _logger.LogInformation($"[TransmitMessage] Nội dung: {request.Message}");

                        // Xử lý tin nhắn tùy theo nội dung
                        await ProcessTransmitMessage(request.ClusterId, request.ServerId, request.Message);

                        _logger.LogInformation($"[TransmitMessage] Đã xử lý tin nhắn thành công từ GameServer");
                        await responseStream.WriteAsync(new TransmitMessageResponse
                        {
                            Success = true,
                            Message = "Đã xử lý tin nhắn thành công"
                        });
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"[TransmitMessage] Lỗi khi xử lý tin nhắn: {ex.Message}");
                        try
                        {
                            await responseStream.WriteAsync(new TransmitMessageResponse
                            {
                                Success = false,
                                Message = $"Lỗi hệ thống: {ex.Message}"
                            });
                        }
                        catch
                        {
                            // Ignore write errors if stream is already closed
                        }
                    }
                }
            }
            catch (System.IO.IOException ioEx) when (ioEx.Message.Contains("request stream was aborted") ||
                                                     ioEx.Message.Contains("connection faulted") ||
                                                     ioEx.Message.Contains("forcibly closed"))
            {
                _logger.LogWarning($"[TransmitMessage] GameServer ngắt kết nối (IOException) - Cluster: {currentClusterId}, Server: {currentServerId}. Chi tiết: {ioEx.Message}");
            }
            catch (ConnectionAbortedException connEx)
            {
                _logger.LogWarning($"[TransmitMessage] GameServer ngắt kết nối (ConnectionAborted) - Cluster: {currentClusterId}, Server: {currentServerId}. Chi tiết: {connEx.Message}");
            }
            catch (Grpc.Core.RpcException rpcEx) when (rpcEx.StatusCode == Grpc.Core.StatusCode.Cancelled ||
                                                       rpcEx.StatusCode == Grpc.Core.StatusCode.Unavailable ||
                                                       rpcEx.StatusCode == Grpc.Core.StatusCode.Aborted)
            {
                _logger.LogWarning($"[TransmitMessage] gRPC connection bị hủy - Cluster: {currentClusterId}, Server: {currentServerId}. Status: {rpcEx.StatusCode}, Chi tiết: {rpcEx.Message}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"[TransmitMessage] Lỗi không mong đợi - Cluster: {currentClusterId}, Server: {currentServerId}. Chi tiết: {ex}");
            }
            finally
            {
                // Cleanup khi stream kết thúc
                if (currentClusterId.HasValue && currentServerId.HasValue)
                {
                    // Đánh dấu server offline khi connection bị mất
                    var server = ClusterManager.Instance.GetServerChannel(currentClusterId.Value, currentServerId.Value);
                    if (server != null)
                    {
                        server.Status = false;
                        server.CurrentPlayers = 0;
                        _logger.LogInformation($"[TransmitMessage] Đã đánh dấu GameServer offline - Cluster: {currentClusterId}, Server: {currentServerId}");
                    }

                    ClusterManager.Instance.RemoveTransmitMessageWriter(currentClusterId.Value, currentServerId.Value);
                    _logger.LogInformation($"[TransmitMessage] Đã cleanup stream writer cho GameServer - Cluster: {currentClusterId}, Server: {currentServerId}");
                }
            }
        }

        public async Task SendMessageToGameServer(IServerStreamWriter<TransmitMessageResponse> responseStream, string message)
        {
            await responseStream.WriteAsync(new TransmitMessageResponse
            {
                Success = true,
                Message = message
            });
        }

        /// <summary>
        /// Xử lý tin nhắn từ GameServer
        /// </summary>
        private async Task ProcessTransmitMessage(int clusterId, int serverId, string message)
        {
            int num = 0;
            try
            {
                string[] array = message.Split('|');
                _logger.LogInformation($"[ProcessTransmitMessage] Processing message: {message}");
                switch (array[0])
                {
                    case "PVP":
                        {
                            // for (int k = 1; k < array.Length; k++)
                            // {
                            //     try
                            //     {
                            //         string[] array7 = array[k].Split(';');
                            //         if (World.PVPList.TryGetValue(array7[0], out int value2))
                            //         {
                            //             World.PVPList.Remove(array7[0]);
                            //         }
                            //         World.PVPList.Add(array7[0], int.Parse(array7[1]));
                            //     }
                            //     catch
                            //     {
                            //     }
                            // }
                            // if (World.PVPList.Count <= 0)
                            // {
                            //     break;
                            // }
                            // int num2 = 0;
                            // string text8 = string.Empty;
                            // foreach (KeyValuePair<string, int> item in World.PVPList.OrderByDescending((KeyValuePair<string, int> pair) => pair.Value))
                            // {
                            //     if (num2 < 10)
                            //     {
                            //         text8 = text8 + "|" + item.Key;
                            //         num2++;
                            //         continue;
                            //     }
                            //     break;
                            // }
                            // foreach (SockClient value5 in World.ServerLst.Values)
                            // {
                            //     value5.Sendd("PVP" + text8);
                            // }
                            // break;
                            break;
                        }
                    case "DECREASE_FACTION_WAR":
                        // Sử dụng ClusterManager để broadcast message đến tất cả GameServer
                        await ClusterManager.Instance.BroadCastPacketMessage("DECREASE_FACTION_WAR|" + array[1] + "|" + array[2],
                            new HashSet<(int, int)> { (clusterId, serverId) }); // Loại trừ server gốc
                        break;
                    case "RDISCONNECTED_FACTION":
                        await ClusterManager.Instance.BroadCastPacketMessage("RDISCONNECTED_FACTION|" + array[1] + "|" + array[2],
                            new HashSet<(int, int)> { (clusterId, serverId) });
                        break;
                    case "FACTION_WAR_PROGRESS":
                        await ClusterManager.Instance.BroadCastPacketMessage("FACTION_WAR_PROGRESS|" + array[1],
                            new HashSet<(int, int)> { (clusterId, serverId) });
                        break;
                    case "FACTION_WAR_TOTAL":
                        await ClusterManager.Instance.BroadCastPacketMessage("FACTION_WAR_TOTAL|" + array[1] + "|" + array[2],
                            new HashSet<(int, int)> { (clusterId, serverId) });
                        break;
                    case "SEND_ANNOUCEMENT":
                        await ClusterManager.Instance.BroadCastPacketMessage("SEND_ANNOUCEMENT|" + array[1] + "|" + array[2],
                            new HashSet<(int, int)> { (clusterId, serverId) });
                        break;
                    case "LION_ROAR":
                        await ClusterManager.Instance.BroadCastPacketMessage("LION_ROAR|" + array[1] + "|" + array[2] + "|" + array[3],
                            new HashSet<(int, int)> { (clusterId, serverId) });
                        break;
                    case "GROUP_QUEST_ACCEPT":
                        try
                        {
                            // Format :  World.conn.Transmit($"GROUP_QUEST_ACCEPT|{UserName}|{this.GuildId}|{questId}");
                            int guildId = int.Parse(array[2]);
                            int questId = int.Parse(array[3]);
                            await GroupQuestManager.Instance.GetOrCreateGuildQuestProgress(guildId, questId);
                        }
                        catch (System.Exception ex)
                        {
                            Logger.Instance.Error("Lỗi xử lý GROUP_QUEST_ACCEPT: " + ex.Message);
                        }
                        break;
                    case "GROUP_QUEST_CANCEL":
                        try
                        {
                            // Format:  World.conn.Transmit($"GROUP_QUEST_CANCEL|{UserName}|{this.GuildId}|{questId}");
                            int guildId = int.Parse(array[2]);
                            int questId = int.Parse(array[3]);
                            await GroupQuestManager.Instance.CancelGuildQuest(guildId, questId);
                        }
                        catch (System.Exception ex)
                        {
                            Logger.Instance.Error("Lỗi xử lý GROUP_QUEST_CANCEL: " + ex.Message);
                        }
                        break;
                    case "GROUP_QUEST_CONTRIBUTION":
                        num = 7;
                        // Xử lý đóng góp quest từ GS
                        // Format: GROUP_QUEST_CONTRIBUTION|QuestType|targetId|targetLevel|GuildID/FactionID|PlayerID|PlayerName|ContributionCount
                        Logger.Instance.Info("GROUP_QUEST_CONTRIBUTION");
                        try
                        {
                        }
                        catch (Exception ex)
                        {
                            Logger.Instance.Error("Lỗi xử lý GROUP_QUEST_CONTRIBUTION: " + ex.Message);
                        }
                        break;
                    case "GROUP_QUEST_COMPLETE":
                        num = 8;
                        // Xử lý hoàn thành quest từ GS
                        // Format: GROUP_QUEST_COMPLETE|QuestType|QuestID|GuildID/FactionID
                        try
                        {
                            int questType = int.Parse(array[1]); // 1: Guild, 2: Faction
                            int questId = int.Parse(array[2]);
                            int groupId = int.Parse(array[3]); // GuildID hoặc FactionID

                            // Gọi phương thức xử lý hoàn thành từ GroupQuestManager
                            if (questType == 1) // Guild Quest
                            {
                                await GroupQuestManager.Instance.CompleteGuildQuest(questId, groupId);
                            }
                            else if (questType == 2) // Faction Quest
                            {
                                await GroupQuestManager.Instance.CompleteFactionQuest(questId, groupId);
                            }

                            // Gửi thông báo đến tất cả GS khác
                            await ClusterManager.Instance.BroadCastPacketMessage("GROUP_QUEST_COMPLETE|" + array[1] + "|" + array[2] + "|" + array[3],
                            new HashSet<(int, int)> { (clusterId, serverId) });
                            // Gửi thông báo đến tất cả người chơi trong guild/faction
                            string questName = "";
                            var questDef = GroupQuestManager.Instance.GetQuestDefinition(questId);
                            if (questDef != null)
                            {
                                questName = questDef.QuestName;
                            }

                            if (questType == 1) // Guild Quest
                            {
                                // Lấy tên guild
                                string guildName = "";
                                // Lấy tên guild từ database thay vì từ đối tượng player
                                try
                                {
                                    // string query = "SELECT G_Name FROM TBL_XWWL_Guild WHERE ID = @ID";
                                    // SqlParameter[] parameters = new SqlParameter[] { new("@ID", groupId) };

                                    // DataTable dt = DBA.GetDBToDataTable(query, "heroGame");
                                    // if (dt != null && dt.Rows.Count > 0)
                                    // {
                                    //     guildName = Convert.ToString(dt.Rows[0]["G_Name"]);
                                    // }
                                    // TODO: Implement guild name lookup using FreeSql GameDb when needed
                                    guildName = $"Guild_{groupId}"; // Temporary placeholder
                                }
                                catch (Exception ex)
                                {
                                    Logger.Instance.Error($"Lỗi lấy tên guild: {ex.Message}");
                                }

                                if (!string.IsNullOrEmpty(guildName))
                                {
                                    await ClusterManager.Instance.BroadCastPacketMessage("GroupQuestMessage|1|" + guildName + "|Nhiệm vụ bang hội [" + questName + "] đã hoàn thành!|10|0",
                                    new HashSet<(int, int)> { (clusterId, serverId) });
                                }
                            }
                            else if (questType == 2) // Faction Quest
                            {
                                await ClusterManager.Instance.BroadCastPacketMessage("GroupQuestMessage|2|" + groupId + "|Nhiệm vụ thế lực [" + questName + "] đã hoàn thành!|10|0",
                                    new HashSet<(int, int)> { (clusterId, serverId) });
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.Instance.Error("Lỗi xử lý GROUP_QUEST_COMPLETE: " + ex.Message);
                        }
                        break;
                    case "MONSTER_DROP":
                        // Sử dụng ClusterManager để broadcast message đến tất cả GameServer
                        await ClusterManager.Instance.BroadCastPacketMessage("MONSTER_DROP|" + array[1] + "|" + array[2] + "|" + array[3],
                            new HashSet<(int, int)> { (clusterId, serverId) }); // Loại trừ server gốc
                        break;
                    case "MONSTER_KILL":
                        {
                            num = 9;
                            // Xử lý sự kiện khi người chơi tiêu diệt quái vật
                            int questType = int.Parse(array[1]); // 1: Guild, 2: Faction
                            int targetId = int.Parse(array[2]);
                            int targetLevel = int.Parse(array[3]);
                            int groupId = int.Parse(array[4]); // GuildID hoặc FactionID
                            int playerId = int.Parse(array[5]);
                            string playerName = array[6];
                            int playerLevel = int.Parse(array[7]);
                            int contributionCount = int.Parse(array[8]);
                            var serverId2 = array[9];
                            //Logger.Instance.Info( $"MONSTER_KILL: serverId={serverId}, targetId={targetId}, targetLevel={targetLevel}, guildId={groupId}, playerId={playerId}, playerName={playerName}, playerLevel={playerLevel}, contributionCount={contributionCount}");

                            // Gọi phương thức xử lý đóng góp từ GroupQuestManager
                            if (questType == 1) // Guild Quest
                            {
                                GroupQuestManager.Instance.SyncGuildQuestContribution(targetId, targetLevel, groupId, playerId, playerName, playerLevel, contributionCount, serverId2);
                            }
                            else if (questType == 2) // Faction Quest
                            {
                                GroupQuestManager.Instance.SyncFactionQuestContribution(targetId, targetLevel, groupId, playerId, playerName, playerLevel, contributionCount);
                            }
                            // }
                        }

                        break;











                    case "PLAYER_KILL":
                        num = 13;
                        try
                        {
                            Logger.Instance.Error($"PLAYER_KILL data: {String.Join(",", array)}");

                            // Xác định số lượng người chơi (mỗi người chơi có 6 thông số)
                            int playerCount = 2; // Mặc định là 2 người chơi (người bị giết và người giết)

                            PlayerPvp[] contributors = new PlayerPvp[playerCount];

                            // Người chơi bị giết (victim)
                            contributors[0] = new PlayerPvp
                            {
                                WorldId = array[1],
                                SessionId = int.Parse(array[2]),
                                PlayerName = array[3],
                                PlayerLevel = int.Parse(array[4]),
                                PlayerGuildId = int.Parse(array[5]),
                                PlayerFactionId = int.Parse(array[6])
                            };

                            // Người chơi giết (killer)
                            contributors[1] = new PlayerPvp
                            {
                                WorldId = array[7],
                                SessionId = int.Parse(array[8]),
                                PlayerName = array[9],
                                PlayerLevel = int.Parse(array[10]),
                                PlayerGuildId = int.Parse(array[11]),
                                PlayerFactionId = int.Parse(array[12])
                            };

                            GroupQuestManager.Instance.OnPlayerKillPlayer(contributors);
                        }
                        catch (Exception ex)
                        {
                            Logger.Instance.Error("Lỗi xử lý PLAYER_KILL: " + ex.Message);
                        }
                        break;
                    case "COUPLE_MESSAGE":
                        num = 8;
                        await ClusterManager.Instance.BroadCastPacketMessage("COUPLE_MESSAGE|" + array[1] + "|" + array[2], [(clusterId, serverId)]);
                        break;
                    case "PK_MESSAGE":
                        num = 8;
                        await ClusterManager.Instance.BroadCastPacketMessage("PK_MESSAGE|" + array[1] + "|" + array[2], [(clusterId, serverId)]);
                        break;
                    case "OPEN_TREASURE":
                        num = 9;
                        await ClusterManager.Instance.BroadCastPacketMessage("OPEN_TREASURE|" + array[1] + "|" + array[2] + "|" + array[3], [(clusterId, serverId)]);
                        break;
                    case "UPDATE_ORIGIN_SERVER":
                        {
                            Logger.Instance.Info($"TODO UPDATE_ORIGIN_SERVER data: {String.Join(",", array)}");
                            // num = 6;
                            // playerS playerS7 = World.QueryPlayer(array[1]);
                            // if (playerS7 != null)
                            // {
                            //     playerS7.OriginalServerSeries = int.Parse(array[2]);
                            //     playerS7.OriginalServerIP = array[3];
                            //     playerS7.OriginalServerPort = array[4];
                            //     playerS7.OriginalServerId = int.Parse(array[5]);
                            // }
                            break;
                        }
                    case "GET_SERVER_LIST":
                        {
                            Logger.Instance.Info($"TODO GET_SERVER_LIST data: {String.Join(",", array)}");
                            // num = 7;
                            // string text2 = array[2];
                            // string str = array[3];
                            // string text3 = array[4];
                            // string text4 = array[5];
                            // bool flag = false;
                            // ServerClass serverClass = new ServerClass();
                            // foreach (ServerClass server in World.ServerList)
                            // {
                            //     foreach (ServerXlClass server2 in server.ServerList)
                            //     {
                            //         if (RxjhClass.IsEquals(server2.SERVER_IP, str))
                            //         {
                            //             serverClass = server;
                            //             flag = true;
                            //             break;
                            //         }
                            //     }
                            //     if (flag)
                            //     {
                            //         break;
                            //     }
                            // }
                            // if (serverClass == null)
                            // {
                            //     break;
                            // }
                            // playerS playerS3 = World.QueryPlayer(array[1]);
                            // if (playerS3 != null)
                            // {
                            //     string text5 = "GET_SERVER_LIST|" + playerS3.UserId + "|";
                            //     for (int i = 0; i < serverClass.ServerList.Count; i++)
                            //     {
                            //         text5 = text5 + serverClass.ServerList[i].ServerId + "," + serverClass.ServerList[i].SERVER_IP + "," + serverClass.ServerList[i].SERVER_PORT + "," + serverClass.ServerList[i].ServerZId + "|";
                            //     }

                            //     await ClusterManager.Instance.SendMessageToOneServer(clusterId, serverId, text5.TrimEnd('|'));
                            // }
                            break;
                        }
                    case "REVIEW_USER_LOGIN":
                        {
                            Logger.Instance.Info($"TODO REVIEW_USER_LOGIN data: {String.Join(",", array)}");
                            // num = 10;
                            // Dictionary<string, playerS> dictionary = new Dictionary<string, playerS>();
                            // string[] array8 = array[1].Split(',');
                            // if (array8.Length <= 1)
                            // {
                            //     break;
                            // }
                            // for (int l = 0; l < array8.Length; l++)
                            // {
                            //     try
                            //     {
                            //         string[] array9 = array8[l].Split('-');
                            //         playerS playerS11 = new playerS();
                            //         playerS11.UserId = array9[0];
                            //         if (array9.Length >= 2)
                            //         {
                            //             playerS11.UserIp = playerS11.UserIp;
                            //         }
                            //         playerS11.ServerID = ServerId;
                            //         playerS11.conn = 0;
                            //         playerS11.BoundAccount = array9[2];
                            //         playerS11.OfflineHangup = array9[3];
                            //         playerS11.UserNmae = array9[4];
                            //         playerS11.OriginalServerSeries = int.Parse(array9[5]);
                            //         playerS11.OriginalServerIP = array9[6];
                            //         playerS11.OriginalServerPort = array9[7];
                            //         playerS11.OriginalServerId = int.Parse(array9[8]);
                            //         playerS11.YinbiSquareServerIP = array9[9];
                            //         playerS11.YinbiSquareServerPort = array9[10];
                            //         playerS11.WorldID = array9[11];
                            //         playerS11.UserIp = array9[1];
                            //         if (World.Players.TryGetValue(playerS11.UserId, out var value3))
                            //         {
                            //             value3.UserIp = array9[1];
                            //             value3.BoundAccount = array9[2];
                            //             value3.OfflineHangup = array9[3];
                            //             value3.UserNmae = array9[4];
                            //             value3.OriginalServerSeries = int.Parse(array9[5]);
                            //             value3.OriginalServerIP = array9[6];
                            //             value3.OriginalServerPort = array9[7];
                            //             value3.OriginalServerId = int.Parse(array9[8]);
                            //             value3.YinbiSquareServerIP = array9[9];
                            //             value3.YinbiSquareServerPort = array9[10];
                            //             value3.WorldID = array9[11];
                            //             value3.GoldToken = array9[12];
                            //             value3.PlayerProfession = array9[13];
                            //         }
                            //         playerS playerS12 = World.QueryAccountMasterTable(array9[0]);
                            //         if (playerS12 != null)
                            //         {
                            //             playerS12.UserIp = array9[1];
                            //             playerS12.BoundAccount = array9[2];
                            //             playerS12.OfflineHangup = array9[3];
                            //             playerS12.UserNmae = array9[4];
                            //             playerS12.OriginalServerSeries = int.Parse(array9[5]);
                            //             playerS12.OriginalServerIP = array9[6];
                            //             playerS12.OriginalServerPort = array9[7];
                            //             playerS12.OriginalServerId = int.Parse(array9[8]);
                            //             playerS12.YinbiSquareServerIP = array9[9];
                            //             playerS12.YinbiSquareServerPort = array9[10];
                            //             playerS12.WorldID = array9[11];
                            //             playerS12.GoldToken = array9[12];
                            //             playerS12.PlayerProfession = array9[13];
                            //         }
                            //         dictionary.Add(array9[0], playerS11);
                            //     }
                            //     catch
                            //     {
                            //     }
                            // }
                            // World.ReviewUserLogin(ServerId, dictionary);
                            break;
                        }
                    case "KICK_PLAYER_USER":
                        // try
                        // {
                        //     num = 11;
                        //     if (World.CheckAndKickPlayer(array[1]))
                        //     {
                        //         await ClusterManager.Instance.SendMessageToOneServer(clusterId, serverId,"KICK_PLAYER_SUCCESS   ID：   " + array[1]);
                        //     }
                        //     else
                        //     {
                        //         await ClusterManager.Instance.SendMessageToOneServer(clusterId, serverId,"KICK_PLAYER_FAILED   ID：   " + array[1] + "   NOT_ONLINE");
                        //     }
                        // }
                        // catch (Exception ex)
                        // {
                        //     Logger.Instance.Error("Lỗi khi xử lý KICK_PLAYER_USER: " + ex.Message);
                        // }
                        break;
                    case "KICK_PLAYER_ID":
                        try
                        {
                            CheckAndKickPlayer(array[2]);
                        }
                        catch
                        {
                        }
                        break;
                    case "CHANGE_CHANNEL_NOTI":
                        {
                            Logger.Instance.Info($"TODO CHANGE_CHANNEL_NOTI data: {String.Join(",", array)}");
                            break;
                            // num = 13;
                            // if (array[2] != "1")
                            // {
                            //     CheckAndKickPlayer(array[1]);
                            //     break;
                            // }
                            // playerS playerS6 = World.QueryPlayer(array[1]);
                            // if (playerS6 != null)
                            // {
                            //     playerS6.封包换线 = int.Parse(array[2]);
                            // }
                            // if (World.PlayerChangeChannelNotification(array[1]) == 0)
                            // {
                            //     await ClusterManager.Instance.SendMessageToOneServer(clusterId, serverId, "USER_KICKOUT|" + array[1]);
                            //     CheckAndKickPlayer(array[1]);
                            // }
                            // break;
                        }
                    case "CHANGE_LINE_LOGIN":
                        {
                            Logger.Instance.Info($"TODO CHANGE_LINE_LOGIN data: {String.Join(",", array)}");
                            // num = 14;
                            // playerS playerS8 = World.QueryPlayer(array[1]);
                            // if (playerS8 != null)
                            // {
                            //     if (playerS8.OfflineHangup == "1")
                            //     {
                            //         await ClusterManager.Instance.SendMessageToOneServer(clusterId, serverId, "OpClient|" + array[4] + "|1");
                            //         return;
                            //     }
                            //     if (playerS8.conn == 0)
                            //     {
                            //         if (playerS8.BoundAccount != "NULL")
                            //         {
                            //             await ClusterManager.Instance.SendMessageToOneServer(clusterId, serverId, "CHANGE_LINE_LOGIN|" + array[1] + "|" + array[2] + "|" + array[3] + "|" + array[4] + "|" + array[5] + "|" + array[6] + "|" + playerS8.BoundAccount + "|" + playerS8.OriginalServerIP + "|" + playerS8.OriginalServerPort + "|" + playerS8.YinbiSquareServerIP + "|" + playerS8.YinbiSquareServerPort + "|" + playerS8.OriginalServerSeries + "|" + playerS8.OriginalServerId + "|" + playerS8.封包换线);
                            //         }
                            //         else
                            //         {
                            //             await ClusterManager.Instance.SendMessageToOneServer(clusterId, serverId, "CHANGE_LINE_LOGIN|" + array[1] + "|" + array[2] + "|" + array[3] + "|" + array[4] + "|" + array[5] + "|" + array[6] + "|" + playerS8.BoundAccount + "|" + array[5] + "|13000|" + array[5] + "|13000|" + playerS8.OriginalServerSeries + "|" + playerS8.OriginalServerId + "|" + playerS8.封包换线);
                            //         }
                            //         return;
                            //     }
                            // }
                            // else
                            // {
                            //     playerS playerS9 = World.QueryAccountMasterTable(array[1]);
                            //     if (playerS9 == null)
                            //     {
                            //         await ClusterManager.Instance.SendMessageToOneServer(clusterId, serverId, "CHANGE_LINE_LOGIN|" + array[1] + "|" + array[2] + "|" + array[3] + "|" + array[4] + "|" + array[5] + "|" + array[6] + "|NULL|" + array[5] + "|13000|127.0.0.1|13000|0|" + array[6] + "|" + playerS8.封包换线);
                            //         playerS playerS10 = new playerS();
                            //         playerS10.UserId = array[1];
                            //         playerS10.ServerID = array[6];
                            //         playerS10.WorldID = array[4];
                            //         playerS10.BoundAccount = "NULL";
                            //         playerS10.OfflineHangup = "0";
                            //         playerS10.UserNmae = "NULL";
                            //         playerS10.UserIp = array[5];
                            //         bool lockTaken = false;
                            //         Dictionary<string, playerS> obj3 = new Dictionary<string, playerS>();
                            //         try
                            //         {
                            //             Monitor.Enter(obj3 = World.Players, ref lockTaken);
                            //             if (World.QueryPlayer(array[1]) == null)
                            //             {
                            //                 World.Players.Add(array[1], playerS10);
                            //             }
                            //         }
                            //         finally
                            //         {
                            //             if (lockTaken)
                            //             {
                            //                 Monitor.Exit(obj3);
                            //             }
                            //         }
                            //         bool lockTaken2 = false;
                            //         try
                            //         {
                            //             Monitor.Enter(obj3 = World.PlayersTemp, ref lockTaken2);
                            //             if (World.QueryAccountMasterTable(array[1]) == null)
                            //             {
                            //                 World.PlayersTemp.Add(array[1], playerS10);
                            //             }
                            //         }
                            //         finally
                            //         {
                            //             if (lockTaken2)
                            //             {
                            //                 Monitor.Exit(obj3);
                            //             }
                            //         }
                            //         return;
                            //     }
                            //     if (playerS9.OfflineHangup == "0")
                            //     {
                            //         await ClusterManager.Instance.SendMessageToOneServer(clusterId, serverId, "CHANGE_LINE_LOGIN|" + array[1] + "|" + array[2] + "|" + array[3] + "|" + array[4] + "|" + array[5] + "|" + array[6] + "|" + playerS9.BoundAccount + "|" + playerS9.OriginalServerIP + "|" + playerS9.OriginalServerPort + "|" + playerS9.YinbiSquareServerIP + "|" + playerS9.YinbiSquareServerPort + "|" + playerS9.OriginalServerSeries + "|" + playerS9.OriginalServerId + "|" + playerS8.封包换线);
                            //         lock (World.Players)
                            //         {
                            //             playerS9.UserIp = array[5];
                            //             World.Players.Add(array[1], playerS9);
                            //         }
                            //         return;
                            //     }
                            // }
                            // await ClusterManager.Instance.SendMessageToOneServer(clusterId, serverId, "USER_KICKOUT|" + array[4]);
                            // World.CheckAndKickPlayer(array[1]);
                            break;
                        }
                    case "CHANGE_LINE_LOGIN_X":
                        {
                            // num = 15;
                            // playerS playerS4 = World.QueryAccountMasterTable(array[1]);
                            // if (playerS4 != null)
                            // {
                            //     playerS4.UserId = array[1];
                            //     playerS4.BoundAccount = array[5];
                            //     playerS4.OfflineHangup = array[6];
                            //     playerS4.UserIp = array[2];
                            //     playerS4.ServerID = array[3];
                            //     playerS4.WorldID = array[4];
                            // }
                            // playerS playerS5 = World.QueryPlayer(array[1]);
                            // if (playerS5 != null)
                            // {
                            //     playerS5.UserId = array[1];
                            //     playerS5.ServerID = array[3];
                            //     playerS5.WorldID = array[4];
                            //     playerS5.BoundAccount = array[5];
                            //     playerS5.OfflineHangup = array[6];
                            //     playerS5.换线完成();
                            //     if (array[7].Length > 10)
                            //     {
                            //         await ClusterManager.Instance.SendMessageToOneServer(clusterId, serverId, "UPDATE_CONFIGURATION_X|" + playerS5.UserId + "|" + array[7]);
                            //     }
                            // }
                            // else
                            // {
                            //     await ClusterManager.Instance.SendMessageToOneServer(clusterId, serverId, "USER_KICKOUT|" + array[4]);
                            //     World.CheckAndKickPlayer(array[1]);
                            // }
                            Logger.Instance.Info($"TODO CHANGE_LINE_LOGIN_X data: {String.Join(",", array)}");
                            break;
                        }
                    case "USER_LOGIN_X":
                        num = 16;
                        if (array.Length == 8)
                        {
                            var gsSession = array[1];
                            var channelId = array[3];
                            var worldId = array[4];
                            var linkedAccount = array[5];
                            var offlineHangup = array[6];
                            var userName = array[7];
                            var userLanIpPort = array[2];

                            var session = LoginServer.Instance.LoginSessions[gsSession];
                            if (session == null)
                            {
                                Logger.Instance.Info($"Session null for user {gsSession}");
                                break;
                            }
                            var account = await AccountDb.FindAccount(gsSession);

                            // Tách các điều kiện để debug
                            bool isSessionValid = session != null;
                            bool isPlayerStatusValid = isSessionValid && session.PlayerStatus == PlayerStatus.SelectedServer;
                            bool isChannelIdValid = isSessionValid && session.SelectedServerId == channelId;
                            bool isLinkedAccountValid = isSessionValid && session.AccountId == linkedAccount;
                            bool isIpValid = isSessionValid && userLanIpPort == session.RemoteEndPoint.Address.ToString();
                            bool isAccountValid = account != null;

                            // Ghi log để kiểm tra từng điều kiện
                            Logger.Instance.Info($"Debug login for user {gsSession}:");
                            Logger.Instance.Info($"  Session valid: {isSessionValid}");
                            Logger.Instance.Info($"  PlayerStatus valid: {isPlayerStatusValid}");
                            Logger.Instance.Info($"  ChannelId valid: {isChannelIdValid}");
                            Logger.Instance.Info($"  LinkedAccount valid: {isLinkedAccountValid}");
                            Logger.Instance.Info($"  IP valid: {isIpValid}");
                            Logger.Instance.Info($"  Account valid: {isAccountValid}");
                            Logger.Instance.Info($" {linkedAccount} {session.AccountId}");
                            Logger.Instance.Info($" {userLanIpPort} {session.RemoteEndPoint.Address.ToString()}");

                            if (isSessionValid && isPlayerStatusValid )
                            {
                                // Check if account is locked
                                if (account.fld_lock == 1)
                                {
                                    Logger.Instance.Info($"Tài khoản {gsSession} đã bị khóa");
                                    LoginServer.Instance.RemoveLoginSession(gsSession);
                                    return;
                                }

                                Logger.Instance.Info($"Tài khoản {account.fld_id} đã đăng nhập từ IP {userLanIpPort}");
                                var mess = "USER_LOGIN_X|" + account.fld_id + "|" + gsSession + "|NOT_ONLINE|" + session.RemoteEndPoint.Address.ToString() + "|" + session.RemoteEndPoint.Port.ToString() + "|127.0.0.1|13000|" + worldId + "|" + worldId;
                                await ClusterManager.Instance.SendMessageToOneServer(clusterId, serverId, mess);
                                session.PlayerStatus = PlayerStatus.LoggedIn;
                            }
                            else
                            {
                                Logger.Instance.Info($"Tài khoản {gsSession} đăng nhập thất bại");
                                LoginServer.Instance.RemoveLoginSession(gsSession);
                            }
                        }
                        break;
                    case "SERVER_CONNECT_LOGIN_X":
                        var ServerId = array[1];
                        var MaximumOnline = int.Parse(array[2]);
                        // Update server status in database
                        var channel = ClusterManager.Instance.GetServerChannel(clusterId, int.Parse(ServerId));
                        if (channel != null)
                        {
                            channel.MaximumOnline = MaximumOnline;
                        }
                        break;
                    case "LION_ROARX":
                        num = 20;
                        Logger.Instance.Info($"TODO LION_ROARX data: {String.Join(",", array)}");
                        // if (World.LionRoarList.Count < World.LionRoarMaximum)
                        // {
                        //     World.LionRoarList.Enqueue(new LionRoarClass
                        //     {
                        //         FLD_INDEX = int.Parse(array[1]),
                        //         UserName = array[2],
                        //         TxtId = World.LionRoarID,
                        //         Txt = array[3],
                        //         UserClientIP = array[4],
                        //         ChannelId = int.Parse(array[5]),
                        //         Map = int.Parse(array[6]),
                        //         样式 = int.Parse(array[7])
                        //     });
                        //     if (World.LionRoarID >= 127)
                        //     {
                        //         World.LionRoarID = 0;
                        //     }
                        //     else
                        //     {
                        //         World.LionRoarID++;
                        //     }
                        // }
                        // else
                        // {
                        //     await ClusterManager.Instance.SendMessageToOneServer(clusterId, serverId, "LION_ROARX|NO|" + array[1]);
                        // }
                        break;
                    case "ChatGuild":
                        Logger.Instance.Info($"TODO ChatGuild data: {String.Join(",", array)}");
                        // num = 20;
                        // if (World.ChatGuildList.Count < World.LionRoarMaximum)
                        // {
                        //     World.ChatGuildList.Enqueue(new LionRoarClass
                        //     {
                        //         FLD_INDEX = int.Parse(array[1]),
                        //         UserName = array[2],
                        //         TxtId = World.LionRoarID,
                        //         Txt = array[3],
                        //         UserClientIP = array[4],
                        //         ChannelId = int.Parse(array[5]),
                        //         GangName = array[6]
                        //     });
                        //     if (World.LionRoarID >= 127)
                        //     {
                        //         World.LionRoarID = 0;
                        //     }
                        //     else
                        //     {
                        //         World.LionRoarID++;
                        //     }
                        // }
                        // else
                        // {
                        //     await ClusterManager.Instance.SendMessageToOneServer(clusterId, serverId, "ChatGuild|NO|" + array[1]);
                        // }
                        break;
                    case "TransmissionMessage":
                        num = 21;
                        await SendBroadCastMessage(array[1], array[2], array[3], array[4], int.Parse(array[5]), array[6]);
                        break;

                    case "GROUP_QUEST_MARK_REWARD":
                        num = 212;
                        // Format: GROUP_QUEST_MARK_REWARD|questType|questId|groupId|playerId
                        if (array.Length >= 5)
                        {
                            int questType = int.Parse(array[1]);
                            int questId = int.Parse(array[2]);
                            int groupId = int.Parse(array[3]);
                            string playerName = array[4];

                            // Đánh dấu đã nhận thưởng
                            GroupQuestManager.Instance.MarkRewardReceived(questType, questId, groupId, playerName);
                        }
                        break;
                    case "Guild_MESSAGE":
                        num = 22;
                        await SendBroadCastGuildMessage(array[1], array[3]);
                        break;
                    case "OFFLINE_HANGUP":
                        {
                            Logger.Instance.Info($"TODO OFFLINE_HANGUP data: {String.Join(",", array)}");
                            // num = 23;
                            // if (World.Players.TryGetValue(array[1], out var value))
                            // {
                            //     value.OfflineHangup = "1";
                            // }
                            // value = World.QueryAccountMasterTable(array[1]);
                            // if (value != null)
                            // {
                            //     value.OfflineHangup = "1";
                            // }
                            break;
                        }
                }

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError($"[ProcessTransmitMessage] Lỗi khi xử lý tin nhắn: {ex.Message}");
            }
        }

        private async Task SendBroadCastGuildMessage(string v1, string v2)
        {
            await ClusterManager.Instance.BroadCastPacketMessage("Guild_MESSAGE|" + v1 + "|" + v2);
        }

        public static async Task SendBroadCastMessage(string 人物全服ID, string name, string toname, string msg, int msgType, string stringHex)
        {
            await ClusterManager.Instance.BroadCastPacketMessage("TransmissionMessage|" + 人物全服ID + "|" + name + "|" + toname + "|" + msg + "|" + msgType + "|" + stringHex);
        }

        public static bool CheckAndKickPlayer(string Userid)
        {
            KickPlayerByServerID(Userid);
            return true;
        }
        public static void KickPlayerByServerID(string userid)
        {
            try
            {
                ClusterManager.Instance.BroadCastPacketMessage("USER_KICKOUT|" + userid);
            }
            catch (Exception)
            {
            }
        }
    }

    public class PlayerPvp
    {
        public string WorldId;

        public int SessionId;
        public string PlayerName;
        public int PlayerLevel;
        public int PlayerGuildId;
        public int PlayerFactionId;

    }
}
