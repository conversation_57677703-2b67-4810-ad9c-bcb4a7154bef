using System;
using System.Collections.Concurrent;
using System.Net;
using System.Net.WebSockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using HeroLogin.Core;
using BCrypt.Net;
using HeroYulgang.Database.FreeSql;

namespace HeroLogin.Services
{
    public class WebSocketService
    {
        private static WebSocketService? _instance;
        private readonly ConfigManager _configManager;
        private readonly ConcurrentDictionary<string, WebSocketConnection> _connections;
        private readonly ConcurrentDictionary<string, WebSocketConnection> _playerConnections;
        private bool _isRunning;

        public static WebSocketService Instance => _instance ??= new WebSocketService();

        public bool IsRunning => _isRunning;
        public int ConnectionCount => _connections.Count;

        private WebSocketService()
        {
            _configManager = ConfigManager.Instance;
            _connections = new ConcurrentDictionary<string, WebSocketConnection>();
            _playerConnections = new ConcurrentDictionary<string, WebSocketConnection>();
            _isRunning = false;
        }

        public void Start()
        {
            if (_isRunning)
            {
                Logger.Instance.Warning("WebSocket service đã đang chạy");
                return;
            }

            try
            {
                _isRunning = true;
                var port = _configManager.LoginServerSettings.WebSocketPort;
                var ip = _configManager.LoginServerSettings.LoginServerIP;
                
                Logger.Instance.Info($"WebSocket service đã khởi động tại ws://{ip}:{port}/ws");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi khởi động WebSocket service: {ex.Message}");
                _isRunning = false;
            }
        }

        public void Stop()
        {
            if (!_isRunning)
            {
                Logger.Instance.Warning("WebSocket service không chạy");
                return;
            }

            try
            {
                // Đóng tất cả kết nối
                foreach (var connection in _connections.Values)
                {
                    connection.Close();
                }

                _connections.Clear();
                _playerConnections.Clear();
                _isRunning = false;
                
                Logger.Instance.Info("WebSocket service đã dừng");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi dừng WebSocket service: {ex.Message}");
            }
        }

        public async Task HandleWebSocketAsync(HttpContext context, WebSocket webSocket)
        {
            var connectionId = Guid.NewGuid().ToString();
            var connection = new WebSocketConnection(connectionId, webSocket, context);
            
            _connections.TryAdd(connectionId, connection);
            
            Logger.Instance.Info($"Client kết nối WebSocket: {context.Connection.RemoteIpAddress}:{context.Connection.RemotePort}");

            try
            {
                await connection.StartListening(HandleMessage);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi trong WebSocket connection {connectionId}: {ex.Message}");
            }
            finally
            {
                // Cleanup khi connection đóng
                _connections.TryRemove(connectionId, out _);
                
                if (!string.IsNullOrEmpty(connection.PlayerId))
                {
                    _playerConnections.TryRemove(connection.PlayerId, out _);
                    Logger.Instance.Info($"Đã xóa liên kết player_id {connection.PlayerId} với WebSocket client");
                }
                
                Logger.Instance.Info($"Client ngắt kết nối WebSocket: {context.Connection.RemoteIpAddress}:{context.Connection.RemotePort}");
            }
        }

        private async Task HandleMessage(WebSocketConnection connection, string message)
        {
            try
            {
                Logger.Instance.Info($"Nhận tin nhắn từ client {connection.ConnectionId}: {message}");

                var request = JsonConvert.DeserializeObject<Dictionary<string, object>>(message);

                if (request == null || !request.ContainsKey("action"))
                {
                    await connection.SendErrorAsync("Thiếu trường 'action' trong yêu cầu", "error");
                    return;
                }

                string action = request["action"].ToString()!;

                switch (action)
                {
                    case "get_server_info":
                        await HandleGetServerInfo(connection);
                        break;

                    case "validate_account":
                        await HandleValidateAccount(connection, request);
                        break;

                    case "get_passkey":
                        await HandleGetPasskey(connection, request);
                        break;

                    case "check_update":
                        await HandleCheckUpdate(connection, request);
                        break;

                    case "get_server_status":
                        await HandleGetServerStatus(connection);
                        break;

                    default:
                        await connection.SendErrorAsync($"Không hỗ trợ action: {action}", "error");
                        break;
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý tin nhắn WebSocket: {ex.Message}");
                await connection.SendErrorAsync($"Lỗi khi xử lý tin nhắn: {ex.Message}", "error");
            }
        }

        public void AssociatePlayerWithConnection(string playerId, WebSocketConnection connection)
        {
            if (string.IsNullOrEmpty(playerId))
                return;

            connection.PlayerId = playerId;
            
            // Nếu player_id này đã liên kết với một connection khác, ghi đè
            if (_playerConnections.ContainsKey(playerId))
            {
                Logger.Instance.Info($"Player_id {playerId} đã được liên kết với một client khác, ghi đè");
            }
            
            _playerConnections.AddOrUpdate(playerId, connection, (key, oldValue) => connection);
            Logger.Instance.Info($"Đã liên kết player_id {playerId} với WebSocket client");
        }

        public async Task NotifyPlayerLogout(string playerId)
        {
            try
            {
                var notification = new Dictionary<string, object>
                {
                    { "action", "character_disconnected" },
                    { "character_id", playerId },
                    { "timestamp", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") }
                };

                string message = JsonConvert.SerializeObject(notification);

                // Gửi thông báo đến tất cả các client đang kết nối
                var tasks = new List<Task>();
                foreach (var connection in _connections.Values)
                {
                    tasks.Add(connection.SendAsync(message));
                }

                await Task.WhenAll(tasks);
                Logger.Instance.Info($"Đã gửi thông báo người chơi {playerId} đăng xuất đến {_connections.Count} client");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi tạo thông báo người chơi đăng xuất: {ex.Message}");
            }
        }

        public async Task NotifyServerStatusChanged()
        {
            try
            {
                var tasks = new List<Task>();
                foreach (var connection in _connections.Values)
                {
                    tasks.Add(HandleGetServerStatus(connection));
                }

                await Task.WhenAll(tasks);
                Logger.Instance.Info($"Đã gửi thông báo cập nhật trạng thái server đến {_connections.Count} client");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi tạo thông báo cập nhật trạng thái server: {ex.Message}");
            }
        }

        // Xử lý yêu cầu lấy thông tin server
        private async Task HandleGetServerInfo(WebSocketConnection connection)
        {
            try
            {
                var serverInfo = new Dictionary<string, object>
                {
                    { "valid", true },
                    { "action", "get_server_info" },
                    { "online_count", LoginServer.Instance.LoginSessions.Count },
                    { "max_accounts_per_ip", 5 }, // Có thể cấu hình từ config
                    { "servers", GetServerList() },
                    { "gameServerIp", _configManager.LoginServerSettings.LoginServerIP },
                    { "gameServerPort", _configManager.LoginServerSettings.LoginServerPort }
                };

                await connection.SendResponseAsync(serverInfo);
                Logger.Instance.Info($"Gửi phản hồi thông tin server đến client {connection.GetClientInfo()}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý yêu cầu thông tin server: {ex.Message}");
                await connection.SendErrorAsync($"Lỗi khi xử lý yêu cầu thông tin server: {ex.Message}", "get_server_info");
            }
        }

        private List<Dictionary<string, object>> GetServerList()
        {
            var serverList = new List<Dictionary<string, object>>();

            foreach (var cluster in _configManager.ServerClusterSettings)
            {
                var serverInfo = new Dictionary<string, object>
                {
                    { "name", cluster.ClusterName },
                    { "channels", new List<Dictionary<string, object>>() }
                };

                var channels = (List<Dictionary<string, object>>)serverInfo["channels"];

                foreach (var channel in cluster.Channels)
                {
                    channels.Add(new Dictionary<string, object>
                    {
                        { "name", channel.ServerName },
                        { "status", channel.Status ? 1 : 0 },
                        { "ip", channel.ServerIP },
                        { "port", channel.GameServerPort },
                        { "id", channel.ServerID },
                        { "available", channel.Status }
                    });
                }

                serverList.Add(serverInfo);
            }

            return serverList;
        }

        // Xử lý yêu cầu xác thực tài khoản
        private async Task HandleValidateAccount(WebSocketConnection connection, Dictionary<string, object> request)
        {
            try
            {
                if (!request.ContainsKey("id") || !request.ContainsKey("password"))
                {
                    await connection.SendErrorAsync("Thiếu thông tin tài khoản hoặc mật khẩu", "validate_account");
                    return;
                }

                string username = request["id"].ToString()!;
                string password = request["password"].ToString()!;

                // Kiểm tra tài khoản trong cơ sở dữ liệu
                bool isValid = await ValidateAccountAsync(username, password);
                string refreshKey = string.Empty;

                if (isValid)
                {
                    // Nếu tài khoản hợp lệ, tạo refresh_key
                    refreshKey = GeneratePasskey(username);
                    // Lưu refresh_key vào cơ sở dữ liệu
                    await SaveRefreshKeyAsync(username, refreshKey);

                    // Liên kết player_id với WebSocket client này
                    AssociatePlayerWithConnection(username, connection);
                }

                var response = new Dictionary<string, object>
                {
                    { "action", "validate_account" },
                    { "id", username },
                    { "valid", isValid },
                    { "refresh_key", refreshKey }
                };

                await connection.SendResponseAsync(response);
                Logger.Instance.Info($"Gửi phản hồi xác thực tài khoản đến client {connection.GetClientInfo()}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xác thực tài khoản: {ex.Message}");
                await connection.SendErrorAsync($"Lỗi khi xác thực tài khoản: {ex.Message}", "validate_account");
            }
        }

        // Xử lý yêu cầu lấy passkey
        private async Task HandleGetPasskey(WebSocketConnection connection, Dictionary<string, object> request)
        {
            try
            {
                if (!request.ContainsKey("id") || !request.ContainsKey("refresh_key"))
                {
                    await connection.SendErrorAsync("Thiếu thông tin tài khoản hoặc refresh_key", "get_passkey");
                    return;
                }

                string username = request["id"].ToString()!;
                string refreshKey = request["refresh_key"].ToString()!;

                // Kiểm tra refresh_key
                if (!await ValidateRefreshKeyAsync(username, refreshKey))
                {
                    await connection.SendErrorAsync("Refresh key không hợp lệ", "get_passkey");
                    return;
                }

                // Tạo passkey cho người dùng
                string passkey = GeneratePasskey(username);
                // Lưu passkey vào cơ sở dữ liệu
                await SavePasskeyAsync(username, passkey);

                var response = new Dictionary<string, object>
                {
                    { "action", "get_passkey" },
                    { "valid", true },
                    { "id", username },
                    { "pass_key", passkey }
                };

                await connection.SendResponseAsync(response);
                Logger.Instance.Info($"Gửi phản hồi passkey đến client {connection.GetClientInfo()}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi lấy passkey: {ex.Message}");
                await connection.SendErrorAsync($"Lỗi khi lấy passkey: {ex.Message}", "get_passkey");
            }
        }

        // Xử lý yêu cầu kiểm tra cập nhật
        private async Task HandleCheckUpdate(WebSocketConnection connection, Dictionary<string, object> request)
        {
            try
            {
                if (!request.ContainsKey("current_version"))
                {
                    await connection.SendErrorAsync("Thiếu thông tin phiên bản hiện tại", "check_update");
                    return;
                }

                string currentVersion = request["current_version"].ToString()!;
                string updateJsonUrl = "https://static.yg-hero.com/update.json";

                // Tạo HttpClient để request dữ liệu từ URL
                using (var httpClient = new HttpClient())
                {
                    httpClient.DefaultRequestHeaders.Add("User-Agent", "YulgangHero Client");
                    httpClient.Timeout = TimeSpan.FromSeconds(5);

                    // Tải dữ liệu JSON từ URL
                    string jsonData = await httpClient.GetStringAsync(updateJsonUrl);
                    Logger.Instance.Info($"Đã tải dữ liệu cập nhật từ {updateJsonUrl}");

                    // Parse dữ liệu JSON
                    var updateInfo = JsonConvert.DeserializeObject<Dictionary<string, string>>(jsonData);

                    if (updateInfo != null)
                    {
                        // Lấy thông tin phiên bản mới nhất và URL cập nhật
                        string latestVersion = updateInfo.GetValueOrDefault("latest_version", "1.0.0");
                        string downloadUrl = updateInfo.GetValueOrDefault("download_url", "");

                        // Kiểm tra xem có cần cập nhật không
                        bool updateRequired = !string.Equals(currentVersion, latestVersion);

                        // Tạo phản hồi
                        var response = new Dictionary<string, object>
                        {
                            { "valid", true },
                            { "action", "check_update" },
                            { "latest_version", latestVersion },
                            { "update_required", updateRequired },
                            { "update_url", downloadUrl }
                        };

                        await connection.SendResponseAsync(response);
                        Logger.Instance.Info($"Gửi phản hồi kiểm tra cập nhật đến client {connection.GetClientInfo()}");
                    }
                    else
                    {
                        await connection.SendErrorAsync("Không thể parse dữ liệu cập nhật", "check_update");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi kiểm tra cập nhật: {ex.Message}");
                await connection.SendErrorAsync($"Lỗi khi kiểm tra cập nhật: {ex.Message}", "check_update");
            }
        }

        // Xử lý yêu cầu lấy trạng thái server
        private async Task HandleGetServerStatus(WebSocketConnection connection)
        {
            try
            {
                // Tạo danh sách thông tin trạng thái server
                var serverStatusInfo = new Dictionary<string, object>
                {
                    { "action", "get_server_status" },
                    { "valid", true },
                    { "timestamp", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") },
                    { "max_accounts_per_ip", 5 }, // Có thể cấu hình từ config
                    { "online_servers", new List<Dictionary<string, object>>() }
                };

                var onlineServers = (List<Dictionary<string, object>>)serverStatusInfo["online_servers"];

                // Thêm thông tin về các server đang online từ cấu hình
                int i = 0;
                foreach (var cluster in _configManager.ServerClusterSettings)
                {
                    foreach (var channel in cluster.Channels)
                    {
                        if (channel.Status)
                        {
                            onlineServers.Add(new Dictionary<string, object>
                            {
                                { "id", i },
                                { "online_count", channel.CurrentPlayers },
                                { "max_online", channel.MaximumOnline }
                            });
                        }
                        i++;
                    }
                }

                await connection.SendResponseAsync(serverStatusInfo);
                Logger.Instance.Info($"Gửi phản hồi trạng thái server đến client {connection.GetClientInfo()}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý yêu cầu trạng thái server: {ex.Message}");
                await connection.SendErrorAsync($"Lỗi khi xử lý yêu cầu trạng thái server: {ex.Message}", "get_server_status");
            }
        }

        // Các phương thức utility
        private async Task<bool> ValidateAccountAsync(string username, string password)
        {
            try
            {
                var account = await AccountDb.FindAccount(username);

                if (account == null)
                    return false;

                // Kiểm tra mật khẩu (giả sử mật khẩu được hash bằng BCrypt)
                return password == account.fld_password;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xác thực tài khoản {username}: {ex.Message}");
                return false;
            }
        }

        private string GeneratePasskey(string username)
        {
            // Tạo passkey ngẫu nhiên cho người dùng
            Random random = new Random();
            string passkey = $"{username}_{DateTime.Now.Ticks}_{random.Next(1000, 9999)}";

            // Mã hóa passkey sử dụng BCrypt
            string hashedPasskey = BCrypt.Net.BCrypt.HashPassword(passkey, workFactor: 12);

            return hashedPasskey;
        }

        private async Task SaveRefreshKeyAsync(string username, string refreshKey)
        {
            try
            {
                var account = await AccountDb.FindAccount(username);

                if (account != null)
                {
                    AccountDb.UpdateRefreshKey(username, refreshKey);
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi lưu refresh key cho {username}: {ex.Message}");
            }
        }

        private async Task<bool> ValidateRefreshKeyAsync(string username, string refreshKey)
        {
            try
            {
                var account = await AccountDb.FindAccount(username);

                if (account == null || string.IsNullOrEmpty(account.fld_refresh_key))
                    return false;

                // Kiểm tra thời gian hết hạn (ví dụ: 24 giờ)
                if (account.fld_refresh_key_timestamp.HasValue)
                {
                    var expireTime = account.fld_refresh_key_timestamp.Value.AddHours(24);
                    if (DateTime.Now > expireTime)
                        return false;
                }

                return account.fld_refresh_key == refreshKey;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xác thực refresh key cho {username}: {ex.Message}");
                return false;
            }
        }

        private async Task SavePasskeyAsync(string username, string passkey)
        {
            try
            {
                var account = await AccountDb.FindAccount(username);

                if (account != null)
                {
                    await AccountDb.UpdatePasskey(username, passkey);
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi lưu passkey cho {username}: {ex.Message}");
            }
        }
    }
}
