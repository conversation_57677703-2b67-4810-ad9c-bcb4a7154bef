using HeroYulgang.Helpers;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;


namespace RxjhServer.AOI
{
    /// <summary>
    /// Advanced caching system for AOI operations to improve performance
    /// </summary>
    public class AOICache
    {
        #region Singleton Pattern
        
        private static readonly Lazy<AOICache> _instance = new Lazy<AOICache>(() => new AOICache());
        public static AOICache Instance => _instance.Value;
        
        #endregion
        
        #region Cache Structures
        
        /// <summary>
        /// Cache for player visibility lists
        /// Key: PlayerSessionID, Value: CachedVisibilityData
        /// </summary>
        private readonly ConcurrentDictionary<int, CachedVisibilityData> _playerVisibilityCache;
        
        /// <summary>
        /// Cache for grid entity counts
        /// Key: GridKey (MapID_GridX_GridY), Value: GridEntityCount
        /// </summary>
        private readonly ConcurrentDictionary<string, GridEntityCount> _gridCountCache;
        
        /// <summary>
        /// Cache for frequently accessed grid combinations
        /// Key: PositionKey (MapID_X_Y), Value: List of grid IDs
        /// </summary>
        private readonly ConcurrentDictionary<string, List<string>> _gridCombinationCache;
        
        /// <summary>
        /// Cache for distance calculations
        /// Key: DistanceKey (X1_Y1_X2_Y2), Value: Distance
        /// </summary>
        private readonly ConcurrentDictionary<string, float> _distanceCache;
        
        #endregion
        
        #region Configuration
        
        /// <summary>
        /// Maximum number of entries in each cache
        /// </summary>
        private const int MAX_CACHE_SIZE = 10000;
        
        /// <summary>
        /// Cache entry expiration time in minutes
        /// </summary>
        private const int CACHE_EXPIRATION_MINUTES = 5;
        
        /// <summary>
        /// Cleanup interval in minutes
        /// </summary>
        private const int CLEANUP_INTERVAL_MINUTES = 2;
        
        #endregion
        
        #region Cache Data Structures
        
        /// <summary>
        /// Cached visibility data for a player
        /// </summary>
        public class CachedVisibilityData
        {
            public HashSet<int> VisiblePlayerIDs { get; set; }
            public HashSet<int> VisibleNPCIDs { get; set; }
            public HashSet<long> VisibleItemIDs { get; set; }
            public DateTime LastUpdate { get; set; }
            public float LastX { get; set; }
            public float LastY { get; set; }
            public int MapID { get; set; }
            public Dictionary<int, long> PlayerStateHashes { get; set; } // Track player state changes

            public CachedVisibilityData()
            {
                VisiblePlayerIDs = new HashSet<int>();
                VisibleNPCIDs = new HashSet<int>();
                VisibleItemIDs = new HashSet<long>();
                PlayerStateHashes = new Dictionary<int, long>();
                LastUpdate = DateTime.Now;
            }

            /// <summary>
            /// Check if cache is still valid based on position, time, and player states
            /// </summary>
            public bool IsValid(float currentX, float currentY, int currentMapID)
            {
                if (currentMapID != MapID)
                    return false;

                if (DateTime.Now - LastUpdate > TimeSpan.FromMinutes(CACHE_EXPIRATION_MINUTES))
                    return false;

                // Check if player moved significantly (more than half AOI radius)
                var distance = Math.Sqrt(Math.Pow(currentX - LastX, 2) + Math.Pow(currentY - LastY, 2));
                if (distance >= AOIManager.AOI_RADIUS / 2)
                    return false;

                // Check if any visible player's state has changed
                foreach (var playerID in VisiblePlayerIDs)
                {
                    if (World.allConnectedChars.TryGetValue(playerID, out var player))
                    {
                        var currentStateHash = CalculatePlayerStateHash(player);
                        if (!PlayerStateHashes.TryGetValue(playerID, out var cachedHash) ||
                            cachedHash != currentStateHash)
                        {
                            return false; // Player state changed, cache invalid
                        }
                    }
                }

                return true;
            }

            /// <summary>
            /// Calculate a hash representing the current state of a player
            /// </summary>
            public long CalculatePlayerStateHash(Players player)
            {
                // Create hash based on visible player properties that affect display
                unchecked
                {
                    long hash = 17;
                    hash = hash * 31 + player.NhanVat_HP.GetHashCode();
                    hash = hash * 31 + player.NhanVat_MP.GetHashCode();
                    hash = hash * 31 + player.Player_Level.GetHashCode();
                    hash = hash * 31 + (player.CharacterName?.GetHashCode() ?? 0);
                    hash = hash * 31 + player.PlayerTuVong.GetHashCode();
                    hash = hash * 31 + player.GMMode.GetHashCode();
                    hash = hash * 31 + (player.Client?.Running.GetHashCode() ?? 0);
                    // Add equipment hash if needed
                    // hash = hash * 31 + player.GetEquipmentHash();
                    return hash;
                }
            }
        }
        
        /// <summary>
        /// Cached entity count for a grid
        /// </summary>
        public class GridEntityCount
        {
            public int PlayerCount { get; set; }
            public int NPCCount { get; set; }
            public int ItemCount { get; set; }
            public DateTime LastUpdate { get; set; }
            
            public int TotalCount => PlayerCount + NPCCount + ItemCount;
            
            public bool IsValid()
            {
                return DateTime.Now - LastUpdate < TimeSpan.FromMinutes(CACHE_EXPIRATION_MINUTES);
            }
        }
        
        #endregion
        
        #region Constructor
        
        private AOICache()
        {
            _playerVisibilityCache = new ConcurrentDictionary<int, CachedVisibilityData>();
            _gridCountCache = new ConcurrentDictionary<string, GridEntityCount>();
            _gridCombinationCache = new ConcurrentDictionary<string, List<string>>();
            _distanceCache = new ConcurrentDictionary<string, float>();
            
            // Start cleanup timer
            StartCleanupTimer();
        }
        
        #endregion
        
        #region Cache Operations
        
        /// <summary>
        /// Get cached visibility data for a player
        /// </summary>
        public CachedVisibilityData GetPlayerVisibility(int sessionID, float currentX, float currentY, int mapID)
        {
            if (_playerVisibilityCache.TryGetValue(sessionID, out var cached))
            {
                if (cached.IsValid(currentX, currentY, mapID))
                {
                    return cached;
                }
                else
                {
                    // Remove invalid cache entry
                    _playerVisibilityCache.TryRemove(sessionID, out _);
                }
            }
            return null;
        }
        
        /// <summary>
        /// Cache visibility data for a player
        /// </summary>
        public void CachePlayerVisibility(int sessionID, float x, float y, int mapID,
            HashSet<int> visiblePlayers, HashSet<int> visibleNPCs, HashSet<long> visibleItems)
        {
            var cacheData = new CachedVisibilityData
            {
                VisiblePlayerIDs = new HashSet<int>(visiblePlayers),
                VisibleNPCIDs = new HashSet<int>(visibleNPCs),
                VisibleItemIDs = new HashSet<long>(visibleItems),
                LastX = x,
                LastY = y,
                MapID = mapID,
                LastUpdate = DateTime.Now
            };

            // Calculate and store current state hashes for all visible players
            foreach (var playerID in visiblePlayers)
            {
                if (World.allConnectedChars.TryGetValue(playerID, out var player))
                {
                    cacheData.PlayerStateHashes[playerID] = cacheData.CalculatePlayerStateHash(player);
                }
            }

            _playerVisibilityCache.AddOrUpdate(sessionID, cacheData, (key, oldValue) => cacheData);

            // Prevent cache from growing too large
            if (_playerVisibilityCache.Count > MAX_CACHE_SIZE)
            {
                CleanupExpiredEntries();
            }
        }
        
        /// <summary>
        /// Get cached grid entity count
        /// </summary>
        public GridEntityCount GetGridEntityCount(string gridKey)
        {
            if (_gridCountCache.TryGetValue(gridKey, out var count) && count.IsValid())
            {
                return count;
            }
            return null;
        }
        
        /// <summary>
        /// Cache grid entity count
        /// </summary>
        public void CacheGridEntityCount(string gridKey, int playerCount, int npcCount, int itemCount)
        {
            var count = new GridEntityCount
            {
                PlayerCount = playerCount,
                NPCCount = npcCount,
                ItemCount = itemCount,
                LastUpdate = DateTime.Now
            };
            
            _gridCountCache.AddOrUpdate(gridKey, count, (key, oldValue) => count);
        }
        
        /// <summary>
        /// Get cached distance calculation
        /// </summary>
        public float? GetCachedDistance(float x1, float y1, float x2, float y2)
        {
            var key = $"{x1:F0}_{y1:F0}_{x2:F0}_{y2:F0}";
            if (_distanceCache.TryGetValue(key, out var distance))
            {
                return distance;
            }
            return null;
        }
        
        /// <summary>
        /// Cache distance calculation
        /// </summary>
        public void CacheDistance(float x1, float y1, float x2, float y2, float distance)
        {
            var key = $"{x1:F0}_{y1:F0}_{x2:F0}_{y2:F0}";
            _distanceCache.TryAdd(key, distance);
            
            // Prevent distance cache from growing too large
            if (_distanceCache.Count > MAX_CACHE_SIZE * 2)
            {
                CleanupDistanceCache();
            }
        }
        
        /// <summary>
        /// Invalidate cache for a specific player
        /// </summary>
        public void InvalidatePlayerCache(int sessionID)
        {
            _playerVisibilityCache.TryRemove(sessionID, out _);
        }

        /// <summary>
        /// Invalidate cache for all players who can see the specified player
        /// This should be called when a player's state changes (HP, equipment, etc.)
        /// </summary>
        public void InvalidateCacheForPlayerStateChange(int changedPlayerID)
        {
            var cachesToInvalidate = new List<int>();

            // Find all caches that contain the changed player
            foreach (var kvp in _playerVisibilityCache)
            {
                if (kvp.Value.VisiblePlayerIDs.Contains(changedPlayerID))
                {
                    cachesToInvalidate.Add(kvp.Key);
                }
            }

            // Remove all affected caches
            foreach (var sessionID in cachesToInvalidate)
            {
                _playerVisibilityCache.TryRemove(sessionID, out _);
            }

            if (cachesToInvalidate.Count > 0)
            {
                LogHelper.WriteLine(LogLevel.Debug,
                    $"Invalidated {cachesToInvalidate.Count} AOI caches due to player {changedPlayerID} state change");
            }
        }

        /// <summary>
        /// Invalidate cache for a specific player
        /// This forces the player to recalculate their AOI on next update
        /// </summary>
        public void InvalidateCacheForPlayer(int playerSessionID)
        {
            _playerVisibilityCache.TryRemove(playerSessionID, out _);
        }
        
        /// <summary>
        /// Invalidate cache for a specific grid
        /// </summary>
        public void InvalidateGridCache(string gridKey)
        {
            _gridCountCache.TryRemove(gridKey, out _);
        }
        
        #endregion
        
        #region Cache Maintenance
        
        /// <summary>
        /// Start the cleanup timer
        /// </summary>
        private void StartCleanupTimer()
        {
            var timer = new System.Threading.Timer(CleanupCallback, null,
                TimeSpan.FromMinutes(CLEANUP_INTERVAL_MINUTES),
                TimeSpan.FromMinutes(CLEANUP_INTERVAL_MINUTES));
        }
        
        /// <summary>
        /// Cleanup callback for timer
        /// </summary>
        private void CleanupCallback(object state)
        {
            try
            {
                CleanupExpiredEntries();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error during AOI cache cleanup: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Clean up expired cache entries
        /// </summary>
        private void CleanupExpiredEntries()
        {
            var expiredTime = DateTime.Now - TimeSpan.FromMinutes(CACHE_EXPIRATION_MINUTES);
            
            // Cleanup player visibility cache
            var expiredPlayers = _playerVisibilityCache
                .Where(kvp => kvp.Value.LastUpdate < expiredTime)
                .Select(kvp => kvp.Key)
                .ToList();
                
            foreach (var sessionID in expiredPlayers)
            {
                _playerVisibilityCache.TryRemove(sessionID, out _);
            }
            
            // Cleanup grid count cache
            var expiredGrids = _gridCountCache
                .Where(kvp => kvp.Value.LastUpdate < expiredTime)
                .Select(kvp => kvp.Key)
                .ToList();
                
            foreach (var gridKey in expiredGrids)
            {
                _gridCountCache.TryRemove(gridKey, out _);
            }
            
            LogHelper.WriteLine(LogLevel.Info, 
                $"AOI Cache cleanup: Removed {expiredPlayers.Count} player entries, {expiredGrids.Count} grid entries");
        }
        
        /// <summary>
        /// Clean up distance cache when it gets too large
        /// </summary>
        private void CleanupDistanceCache()
        {
            // Remove half of the entries randomly to keep cache size manageable
            var keysToRemove = _distanceCache.Keys.Take(_distanceCache.Count / 2).ToList();
            foreach (var key in keysToRemove)
            {
                _distanceCache.TryRemove(key, out _);
            }
        }
        
        /// <summary>
        /// Get cache statistics
        /// </summary>
        public string GetCacheStatistics()
        {
            return $"AOI Cache Stats - Players: {_playerVisibilityCache.Count}, " +
                   $"Grids: {_gridCountCache.Count}, " +
                   $"Distances: {_distanceCache.Count}, " +
                   $"GridCombos: {_gridCombinationCache.Count}";
        }
        
        /// <summary>
        /// Clear all caches
        /// </summary>
        public void ClearAllCaches()
        {
            _playerVisibilityCache.Clear();
            _gridCountCache.Clear();
            _gridCombinationCache.Clear();
            _distanceCache.Clear();
            
            LogHelper.WriteLine(LogLevel.Info, "All AOI caches cleared");
        }
        
        #endregion
    }
}
