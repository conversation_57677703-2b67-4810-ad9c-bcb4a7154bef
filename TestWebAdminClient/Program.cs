using Grpc.Net.Client;
using HeroLogin.Protos;

namespace TestWebAdminClient
{
    class Program
    {
        static async Task Main(string[] args)
        {
            // Create gRPC channel
            using var channel = GrpcChannel.ForAddress("http://localhost:6999");
            var client = new WebAdmin.WebAdminClient(channel);

            try
            {
                Console.WriteLine("=== Testing WebAdmin gRPC Service ===\n");

                // Test 1: Login
                Console.WriteLine("1. Testing Login...");
                var loginRequest = new LoginRequest
                {
                    Username = "admin",
                    Password = "admin123",
                    IpAddress = "127.0.0.1"
                };

                var loginResponse = await client.LoginAsync(loginRequest);
                Console.WriteLine($"Login Success: {loginResponse.Success}");
                Console.WriteLine($"Message: {loginResponse.Message}");

                if (!loginResponse.Success)
                {
                    Console.WriteLine("Login failed, stopping tests.");
                    return;
                }

                Console.WriteLine($"Access Token: {loginResponse.AccessToken[..Math.Min(20, loginResponse.AccessToken.Length)]}...");
                Console.WriteLine($"User: {loginResponse.User.Username} ({loginResponse.User.FirstName} {loginResponse.User.LastName})");
                Console.WriteLine($"Roles: {string.Join(", ", loginResponse.User.Roles)}");
                Console.WriteLine();

                // Test 2: Get Server List
                Console.WriteLine("2. Testing Get Server List...");
                var headers = new Grpc.Core.Metadata
                {
                    { "authorization", $"Bearer {loginResponse.AccessToken}" }
                };

                var serverListRequest = new GetServerListRequest();
                var serverListResponse = await client.GetServerListAsync(serverListRequest, headers);

                Console.WriteLine($"Get Server List Success: {serverListResponse.Success}");
                Console.WriteLine($"Message: {serverListResponse.Message}");
                Console.WriteLine($"Server Count: {serverListResponse.Servers.Count}");

                foreach (var server in serverListResponse.Servers)
                {
                    Console.WriteLine($"  - Server {server.ServerId}: {server.ServerName} ({server.Status})");
                    Console.WriteLine($"    IP: {server.ServerIp}:{server.GameServerPort}");
                    Console.WriteLine($"    Players: {server.OnlinePlayers}/{server.MaxPlayers}");
                }
                Console.WriteLine();

                // Test 3: Get Server Status (if servers exist)
                if (serverListResponse.Servers.Count > 0)
                {
                    var firstServer = serverListResponse.Servers[0];
                    Console.WriteLine($"3. Testing Get Server Status for Server {firstServer.ServerId}...");

                    var statusRequest = new GetServerStatusRequest { ServerId = firstServer.ServerId };
                    var statusResponse = await client.GetServerStatusAsync(statusRequest, headers);

                    Console.WriteLine($"Get Server Status Success: {statusResponse.Success}");
                    Console.WriteLine($"Message: {statusResponse.Message}");

                    if (statusResponse.Success && statusResponse.Server != null)
                    {
                        Console.WriteLine($"Server: {statusResponse.Server.ServerName}");
                        Console.WriteLine($"Status: {statusResponse.Server.Status}");
                        Console.WriteLine($"Uptime: {statusResponse.Server.Uptime}");

                        if (statusResponse.Metrics != null)
                        {
                            Console.WriteLine($"CPU Usage: {statusResponse.Metrics.CpuUsage:F2}%");
                            Console.WriteLine($"Memory Usage: {statusResponse.Metrics.MemoryUsage:F2} MB");
                        }
                    }
                    Console.WriteLine();
                }

                // Test 4: Logout
                Console.WriteLine("4. Testing Logout...");
                var logoutRequest = new LogoutRequest { AccessToken = loginResponse.AccessToken };
                var logoutResponse = await client.LogoutAsync(logoutRequest);

                Console.WriteLine($"Logout Success: {logoutResponse.Success}");
                Console.WriteLine($"Message: {logoutResponse.Message}");
                Console.WriteLine();

                Console.WriteLine("=== All tests completed successfully ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error during testing: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
            }
        }
    }
}